apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: svami-orchestrator
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-throttling: "false"
        autoscaling.knative.dev/maxScale: "10"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 10
      timeoutSeconds: 300
      containers:
      - image: us-central1-docker.pkg.dev/konveyn2ai/konveyn2ai-repo/svami-orchestrator:latest
        ports:
        - name: http1
          containerPort: 8080
        env:
        - name: PROJECT_ID
          value: "konveyn2ai"
        - name: REGION
          value: "us-central1"
        - name: SERVICE_NAME
          value: "svami-orchestrator"
        - name: AMATYA_SERVICE_URL
          value: "https://amatya-role-prompter-***********.us-central1.run.app"
        - name: JANAPADA_SERVICE_URL
          value: "https://janapada-memory-***********.us-central1.run.app"
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "1"
            memory: "1Gi"
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          timeoutSeconds: 5
          periodSeconds: 10
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          timeoutSeconds: 5
          periodSeconds: 60
          failureThreshold: 3
