{"data_mtime": 1753508936, "dep_lines": [3, 13, 15, 16, 1, 4, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "urllib.parse", "starlette.concurrency", "starlette.types", "__future__", "shlex", "typing", "builtins", "_collections_abc", "_typeshed", "abc", "typing_extensions", "urllib"], "hash": "ce16c619c99e45507a3aebedf47c2807c8122bd9396e21f7cd48636f9715fa8c", "id": "starlette.datastructures", "ignore_all": true, "interface_hash": "277dd4960d0b7e157282bcc806cc1c3fac6d8f6b3a0744b3d02a4b559915fcf5", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/datastructures.py", "plugin_data": null, "size": 22465, "suppressed": [], "version_id": "1.8.0"}