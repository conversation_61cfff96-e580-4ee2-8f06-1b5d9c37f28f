{".class": "MypyFile", "_fullname": "starlette.datastructures", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.Address", "name": "Address", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "starlette.datastructures.Address", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["host", "port"]}}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.Address", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.datastructures.Address.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.datastructures.Address.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "host", "port"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "starlette.datastructures.Address.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "host", "port"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Address", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Address._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of Address", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.datastructures.Address._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.datastructures.Address._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.datastructures.Address._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "starlette.datastructures.Address._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Address", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "starlette.datastructures.Address._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Address", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "host", "port"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Address._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "host", "port"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of Address", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.datastructures.Address._source", "name": "_source", "type": "builtins.str"}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "starlette.datastructures.Address.host", "name": "host", "type": "builtins.str"}}, "host-redefinition": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.Address.host", "kind": "<PERSON><PERSON><PERSON>"}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "starlette.datastructures.Address.port", "name": "port", "type": "builtins.int"}}, "port-redefinition": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.Address.port", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Address.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": "starlette.datastructures.Address"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef"}, "CommaSeparatedStrings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.CommaSeparatedStrings", "name": "CommaSeparatedStrings", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.CommaSeparatedStrings", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.CommaSeparatedStrings", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.CommaSeparatedStrings.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.CommaSeparatedStrings", {".class": "UnionType", "items": ["builtins.int", "builtins.slice"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of CommaSeparatedStrings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.CommaSeparatedStrings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["starlette.datastructures.CommaSeparatedStrings", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CommaSeparatedStrings", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.CommaSeparatedStrings.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.CommaSeparatedStrings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of CommaSeparatedStrings", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.CommaSeparatedStrings.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.CommaSeparatedStrings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of CommaSeparatedStrings", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.CommaSeparatedStrings.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.CommaSeparatedStrings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of CommaSeparatedStrings", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.CommaSeparatedStrings.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.CommaSeparatedStrings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of CommaSeparatedStrings", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.CommaSeparatedStrings._items", "name": "_items", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.CommaSeparatedStrings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.CommaSeparatedStrings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FormData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["starlette.datastructures.UploadFile", "builtins.str"]}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.FormData", "name": "FormData", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.FormData", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.FormData", "starlette.datastructures.ImmutableMultiDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.FormData.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["starlette.datastructures.FormData", {".class": "UnionType", "items": ["starlette.datastructures.FormData", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "starlette.datastructures.UploadFile"]}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "starlette.datastructures.UploadFile"]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}]}, {".class": "UnionType", "items": ["builtins.str", "starlette.datastructures.UploadFile"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FormData", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.datastructures.FormData.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.FormData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of FormData", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.FormData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.FormData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.Headers", "name": "Headers", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.Headers", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.Headers", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.Headers", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of Headers", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.Headers", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of <PERSON>ers", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.Headers", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of <PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "headers", "raw", "scope"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "headers", "raw", "scope"], "arg_types": ["starlette.datastructures.Headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>ers", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of <PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of <PERSON><PERSON>", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of <PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.Headers._list", "name": "_list", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "getlist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.getlist", "name": "getlist", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["starlette.datastructures.Headers", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getlist of Headers", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of Headers", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keys of Headers", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "mutablecopy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.mutablecopy", "name": "mutablecopy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mutablecopy of Headers", "ret_type": "starlette.datastructures.MutableHeaders", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.Headers.raw", "name": "raw", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw of Headers", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.Headers.raw", "name": "raw", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw of Headers", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Headers.values", "name": "values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "values of Headers", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Headers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.Headers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImmutableMultiDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.ImmutableMultiDict", "name": "ImmutableMultiDict", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.ImmutableMultiDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of ImmutableMultiDict", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of ImmutableMultiDict", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of ImmutableMultiDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImmutableMultiDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of ImmutableMultiDict", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of ImmutableMultiDict", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ImmutableMultiDict", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.datastructures.ImmutableMultiDict._dict", "name": "_dict", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "builtins.dict"}}}, "_list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.ImmutableMultiDict._list", "name": "_list", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "getlist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.getlist", "name": "getlist", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getlist of ImmutableMultiDict", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of ImmutableMultiDict", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "typing.ItemsView"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keys of ImmutableMultiDict", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "<PERSON>.<PERSON><PERSON><PERSON>"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "multi_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.multi_items", "name": "multi_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multi_items of ImmutableMultiDict", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.ImmutableMultiDict.values", "name": "values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "values of ImmutableMultiDict", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "typing.ValuesView"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.ImmutableMultiDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "id": 1, "name": "_KeyType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "id": 2, "name": "_CovariantValueType", "namespace": "starlette.datastructures.ImmutableMultiDict", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_KeyType", "_CovariantValueType"], "typeddict_type": null}}, "ItemsView": {".class": "SymbolTableNode", "cross_ref": "typing.ItemsView", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "KeysView": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MultiDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.MultiDict", "name": "MultiDict", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.MultiDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.MultiDict", "starlette.datastructures.ImmutableMultiDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.MultiDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of MultiDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["starlette.datastructures.MultiDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of MultiDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["starlette.datastructures.MultiDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of MultiDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.MultiDict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of MultiDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "default"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.pop", "name": "pop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "default"], "arg_types": ["starlette.datastructures.MultiDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop of MultiDict", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "popitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.popitem", "name": "popitem", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.MultiDict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "popitem of MultiDict", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "poplist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.poplist", "name": "poplist", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["starlette.datastructures.MultiDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "poplist of MultiDict", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "setdefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "default"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.setdefault", "name": "set<PERSON><PERSON>ult", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "default"], "arg_types": ["starlette.datastructures.MultiDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdefault of MultiDict", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "setlist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.setlist", "name": "setlist", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "values"], "arg_types": ["starlette.datastructures.MultiDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setlist of MultiDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MultiDict.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["starlette.datastructures.MultiDict", {".class": "UnionType", "items": ["starlette.datastructures.MultiDict", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of MultiDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.MultiDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.MultiDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableHeaders": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.datastructures.Headers"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.MutableHeaders", "name": "MutableHeaders", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.MutableHeaders", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.MutableHeaders", "starlette.datastructures.Headers", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MutableHeaders.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.MutableHeaders", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of MutableHeaders", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__ior__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MutableHeaders.__ior__", "name": "__ior__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.MutableHeaders", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ior__ of MutableHeaders", "ret_type": "starlette.datastructures.MutableHeaders", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MutableHeaders.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.MutableHeaders", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__or__ of MutableHeaders", "ret_type": "starlette.datastructures.MutableHeaders", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MutableHeaders.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["starlette.datastructures.MutableHeaders", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of MutableHeaders", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_vary_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vary"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MutableHeaders.add_vary_header", "name": "add_vary_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "vary"], "arg_types": ["starlette.datastructures.MutableHeaders", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_vary_header of MutableHeaders", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MutableHeaders.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["starlette.datastructures.MutableHeaders", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of MutableHeaders", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.MutableHeaders.raw", "name": "raw", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.MutableHeaders"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw of MutableHeaders", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.MutableHeaders.raw", "name": "raw", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.MutableHeaders"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw of MutableHeaders", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "setdefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MutableHeaders.setdefault", "name": "set<PERSON><PERSON>ult", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["starlette.datastructures.MutableHeaders", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdefault of MutableHeaders", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.MutableHeaders.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["starlette.datastructures.MutableHeaders", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of MutableHeaders", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.MutableHeaders.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.MutableHeaders", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "QueryParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "starlette.datastructures.ImmutableMultiDict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.QueryParams", "name": "QueryParams", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.QueryParams", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.QueryParams", "starlette.datastructures.ImmutableMultiDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.QueryParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["starlette.datastructures.QueryParams", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "starlette.datastructures.ImmutableMultiDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "builtins.str", "builtins.bytes"]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QueryParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.QueryParams.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.QueryParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of QueryParams", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.QueryParams.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.QueryParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of QueryParams", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.QueryParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.QueryParams", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Scope": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Scope", "kind": "Gdef"}, "Secret": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.Secret", "name": "Secret", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.Secret", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.Secret", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Secret.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.Secret"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of Secret", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Secret.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["starlette.datastructures.Secret", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Secret", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Secret.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.Secret"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Secret", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.Secret.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.Secret"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Secret", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.Secret._value", "name": "_value", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.Secret.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.Secret", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SplitResult": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.SplitResult", "kind": "Gdef"}, "State": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.State", "name": "State", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.State", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.State", "builtins.object"], "names": {".class": "SymbolTable", "__delattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.State.__delattr__", "name": "__delattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.State", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delattr__ of State", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.State.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.State", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of State", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.State.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "state"], "arg_types": ["starlette.datastructures.State", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of State", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.State.__setattr__", "name": "__setattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["starlette.datastructures.State", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setattr__ of State", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.datastructures.State._state", "name": "_state", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.State.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.State", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.URL", "name": "URL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.URL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.URL", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URL.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.datastructures.URL", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of URL", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "url", "scope", "components"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URL.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "url", "scope", "components"], "arg_types": ["starlette.datastructures.URL", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of URL", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URL.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URL.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_components": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.URL._components", "name": "_components", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "urllib.parse.SplitResult"}}}, "_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.URL._url", "name": "_url", "type": "builtins.str"}}, "components": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.components", "name": "components", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "components of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib.parse.SplitResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.components", "name": "components", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "components of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib.parse.SplitResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "fragment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.fragment", "name": "fragment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fragment of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.fragment", "name": "fragment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fragment of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.hostname", "name": "hostname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hostname of URL", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.hostname", "name": "hostname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hostname of URL", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "include_query_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URL.include_query_params", "name": "include_query_params", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["starlette.datastructures.URL", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "include_query_params of URL", "ret_type": "starlette.datastructures.URL", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_secure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.is_secure", "name": "is_secure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_secure of URL", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.is_secure", "name": "is_secure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_secure of URL", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "netloc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.netloc", "name": "netloc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "netloc of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.netloc", "name": "netloc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "netloc of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.password", "name": "password", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "password of URL", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.password", "name": "password", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "password of URL", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.port", "name": "port", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "port of URL", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.port", "name": "port", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "port of URL", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "remove_query_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URL.remove_query_params", "name": "remove_query_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["starlette.datastructures.URL", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_query_params of URL", "ret_type": "starlette.datastructures.URL", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URL.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["starlette.datastructures.URL", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace of URL", "ret_type": "starlette.datastructures.URL", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "replace_query_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URL.replace_query_params", "name": "replace_query_params", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["starlette.datastructures.URL", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_query_params of URL", "ret_type": "starlette.datastructures.URL", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.scheme", "name": "scheme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scheme of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.scheme", "name": "scheme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scheme of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "username": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.URL.username", "name": "username", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "username of URL", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.URL.username", "name": "username", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "username of URL", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.URL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.URL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "URLPath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.URLPath", "name": "URLPath", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.URLPath", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.URLPath", "builtins.str", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "protocol", "host"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URLPath.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "protocol", "host"], "arg_types": ["starlette.datastructures.URLPath", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of URLPath", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "path", "protocol", "host"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "starlette.datastructures.URLPath.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "path", "protocol", "host"], "arg_types": [{".class": "TypeType", "item": "starlette.datastructures.URLPath"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of URLPath", "ret_type": "starlette.datastructures.URLPath", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.URLPath.host", "name": "host", "type": "builtins.str"}}, "make_absolute_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "base_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.URLPath.make_absolute_url", "name": "make_absolute_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "base_url"], "arg_types": ["starlette.datastructures.URLPath", {".class": "UnionType", "items": ["builtins.str", "starlette.datastructures.URL"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_absolute_url of URLPath", "ret_type": "starlette.datastructures.URL", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "protocol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.URLPath.protocol", "name": "protocol", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.URLPath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.URLPath", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UploadFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.datastructures.UploadFile", "name": "UploadFile", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.datastructures.UploadFile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.datastructures", "mro": ["starlette.datastructures.UploadFile", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "file", "size", "filename", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.UploadFile.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "file", "size", "filename", "headers"], "arg_types": ["starlette.datastructures.UploadFile", "typing.BinaryIO", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["starlette.datastructures.Headers", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UploadFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.UploadFile.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.datastructures.UploadFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of UploadFile", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_in_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.UploadFile._in_memory", "name": "_in_memory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.UploadFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_in_memory of UploadFile", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.UploadFile._in_memory", "name": "_in_memory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.UploadFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_in_memory of UploadFile", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_max_mem_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.UploadFile._max_mem_size", "name": "_max_mem_size", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"]}}}, "_will_roll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "size_to_add"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.datastructures.UploadFile._will_roll", "name": "_will_roll", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "size_to_add"], "arg_types": ["starlette.datastructures.UploadFile", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_will_roll of UploadFile", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.datastructures.UploadFile.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.UploadFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of UploadFile", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "content_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.datastructures.UploadFile.content_type", "name": "content_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.UploadFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "content_type of UploadFile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.datastructures.UploadFile.content_type", "name": "content_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.datastructures.UploadFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "content_type of UploadFile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.UploadFile.file", "name": "file", "type": "typing.BinaryIO"}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.UploadFile.filename", "name": "filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.UploadFile.headers", "name": "headers", "type": "starlette.datastructures.Headers"}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.datastructures.UploadFile.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "size"], "arg_types": ["starlette.datastructures.UploadFile", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of UploadFile", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "seek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.datastructures.UploadFile.seek", "name": "seek", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "offset"], "arg_types": ["starlette.datastructures.UploadFile", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seek of UploadFile", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.datastructures.UploadFile.size", "name": "size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.datastructures.UploadFile.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["starlette.datastructures.UploadFile", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of UploadFile", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures.UploadFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.datastructures.UploadFile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValuesView": {".class": "SymbolTableNode", "cross_ref": "typing.ValuesView", "kind": "Gdef"}, "_CovariantValueType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._CovariantValueType", "name": "_CovariantValueType", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "_KeyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.datastructures._KeyType", "name": "_KeyType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.datastructures.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.datastructures.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.datastructures.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.datastructures.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.datastructures.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "parse_qsl": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qsl", "kind": "Gdef"}, "run_in_threadpool": {".class": "SymbolTableNode", "cross_ref": "starlette.concurrency.run_in_threadpool", "kind": "Gdef"}, "shlex": {".class": "SymbolTableNode", "cross_ref": "shlex.shlex", "kind": "Gdef"}, "urlencode": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlencode", "kind": "Gdef"}, "urlsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlsplit", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/datastructures.py"}