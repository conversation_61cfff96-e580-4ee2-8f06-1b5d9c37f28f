{"data_mtime": 1753508936, "dep_lines": [4, 11, 13, 18, 21, 23, 24, 25, 26, 27, 28, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 14, 15, 16, 17, 20, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["http.cookies", "collections.abc", "email.utils", "urllib.parse", "anyio.to_thread", "starlette._utils", "starlette.background", "starlette.concurrency", "starlette.datastructures", "starlette.requests", "starlette.types", "__future__", "<PERSON><PERSON><PERSON>", "http", "json", "os", "re", "stat", "sys", "warnings", "datetime", "functools", "mimetypes", "secrets", "typing", "anyio", "builtins", "_typeshed", "_warnings", "abc", "typing_extensions", "urllib"], "hash": "4a6c5ce19ba6fb1ecb1b393aae0eeaa6ed183c7163061d36117317dd9176049b", "id": "starlette.responses", "ignore_all": true, "interface_hash": "3670aa058f56182471bc4907e8e0218cffb608f4be554bfa4ab79b080d7998e5", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/responses.py", "plugin_data": null, "size": 20731, "suppressed": [], "version_id": "1.8.0"}