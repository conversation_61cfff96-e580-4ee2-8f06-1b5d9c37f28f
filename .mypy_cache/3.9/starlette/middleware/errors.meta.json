{"data_mtime": 1753508936, "dep_lines": [8, 9, 10, 11, 12, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["starlette._utils", "starlette.concurrency", "starlette.requests", "starlette.responses", "starlette.types", "__future__", "html", "inspect", "sys", "traceback", "builtins", "abc", "starlette.websockets", "types", "typing"], "hash": "87be937d50eb7584afa410045a067dd55bcf6507b7bd1a4097a0a1a225c6f939", "id": "starlette.middleware.errors", "ignore_all": true, "interface_hash": "0990e97a750988446dcc66887c58d41f2e2b99cacd0affff8dc3093fa450c1b5", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/middleware/errors.py", "plugin_data": null, "size": 8037, "suppressed": [], "version_id": "1.8.0"}