{"data_mtime": 1753508936, "dep_lines": [3, 8, 9, 10, 11, 1, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "starlette._utils", "starlette.requests", "starlette.responses", "starlette.types", "__future__", "typing", "anyio", "builtins", "abc", "anyio._core", "anyio._core._exceptions", "anyio._core._streams", "anyio._core._synchronization", "anyio._core._tasks", "anyio._core._typedattr", "anyio.abc", "anyio.abc._resources", "anyio.abc._streams", "anyio.abc._tasks", "anyio.streams", "anyio.streams.memory", "contextlib", "starlette.background"], "hash": "e30dabe4f2b9d476363dd31828cbd4c650a0f597aea6ae7c93476718b30057df", "id": "starlette.middleware.base", "ignore_all": true, "interface_hash": "c10fbac10d4db3804b7ef0bc997315b2f754e834520bf0bf7ffef85231d98bfa", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/middleware/base.py", "plugin_data": null, "size": 9631, "suppressed": [], "version_id": "1.8.0"}