{"data_mtime": 1753508936, "dep_lines": [4, 12, 1, 3, 5, 10, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "starlette.types", "__future__", "sys", "typing", "typing_extensions", "builtins", "_typeshed", "abc"], "hash": "dd696371f0039d296d26b554b85829bc989929c8ec8c2d4887d6aa614bd29279", "id": "starlette.middleware", "ignore_all": true, "interface_hash": "b4791b6be3349690536f1d08530524807b704b86bfe3410469e69870ed9a0bd4", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/middleware/__init__.py", "plugin_data": null, "size": 1224, "suppressed": [], "version_id": "1.8.0"}