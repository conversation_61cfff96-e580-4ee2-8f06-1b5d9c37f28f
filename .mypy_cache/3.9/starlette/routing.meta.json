{"data_mtime": 1753508936, "dep_lines": [10, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 3, 4, 5, 6, 7, 8, 9, 12, 14, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "starlette._exception_handler", "starlette._utils", "starlette.concurrency", "starlette.convertors", "starlette.datastructures", "starlette.exceptions", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.types", "starlette.websockets", "__future__", "contextlib", "functools", "inspect", "re", "traceback", "types", "warnings", "enum", "typing", "builtins", "_typeshed", "_warnings", "abc", "typing_extensions"], "hash": "21f7c91fa479e1db8342d65cbb715e88663ff41a288bf020f9788cc4750cb068", "id": "starlette.routing", "ignore_all": true, "interface_hash": "37ddb442113ba394afccbb79ecdb69d10f961d6eee3bb0ad85d11aec7f959dd5", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/routing.py", "plugin_data": null, "size": 34213, "suppressed": [], "version_id": "1.8.0"}