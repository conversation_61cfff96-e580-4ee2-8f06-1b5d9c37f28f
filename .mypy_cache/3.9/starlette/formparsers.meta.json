{"data_mtime": 1753508936, "dep_lines": [3, 8, 10, 1, 4, 5, 6, 7, 1, 1, 1, 14, 13], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 25, 25], "dependencies": ["collections.abc", "urllib.parse", "starlette.datastructures", "__future__", "dataclasses", "enum", "tempfile", "typing", "builtins", "abc", "typing_extensions"], "hash": "35d9797465d9b68a732548ccd38339cd8852f2c4f7ddeff92702b64fb31de330", "id": "starlette.formparsers", "ignore_all": true, "interface_hash": "a07890a557ea579dc825eef1383313f75b9123c9686445ab5770163c3d6f5eaf", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/formparsers.py", "plugin_data": null, "size": 11086, "suppressed": ["python_multipart.multipart", "python_multipart"], "version_id": "1.8.0"}