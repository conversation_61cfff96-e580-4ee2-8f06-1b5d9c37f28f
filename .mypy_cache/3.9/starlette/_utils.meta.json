{"data_mtime": 1753508936, "dep_lines": [6, 10, 1, 3, 4, 5, 7, 8, 15, 1, 1, 1, 20], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 5], "dependencies": ["collections.abc", "starlette.types", "__future__", "functools", "inspect", "sys", "contextlib", "typing", "typing_extensions", "builtins", "_typeshed", "abc"], "hash": "3b10d61f59d522c14106f170cc21999104fa6d45dfbe38c775cd5770bb5292c8", "id": "starlette._utils", "ignore_all": true, "interface_hash": "630aaa7f68c865deb46944c6994c7bfb53799c25c9fa4d8af872b206a879c9c8", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/_utils.py", "plugin_data": null, "size": 2748, "suppressed": ["exceptiongroup"], "version_id": "1.8.0"}