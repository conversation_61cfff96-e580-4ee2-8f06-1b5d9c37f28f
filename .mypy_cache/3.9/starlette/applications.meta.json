{"data_mtime": **********, "dep_lines": [15, 16, 17, 5, 13, 14, 18, 19, 20, 21, 22, 1, 3, 4, 6, 11, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["starlette.middleware.base", "starlette.middleware.errors", "starlette.middleware.exceptions", "collections.abc", "starlette.datastructures", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.routing", "starlette.types", "starlette.websockets", "__future__", "sys", "warnings", "typing", "typing_extensions", "builtins", "_typeshed", "abc", "contextlib"], "hash": "0098f3d620c01b14ce62a3b954089b2cc415b87090b59173e706d4b75e01be76", "id": "starlette.applications", "ignore_all": true, "interface_hash": "bb9a32aef31cf308d3d354d452ea0468d4078eda7b10620a6a8c8becc643eb38", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/applications.py", "plugin_data": null, "size": 10515, "suppressed": [], "version_id": "1.8.0"}