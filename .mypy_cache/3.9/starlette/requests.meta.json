{"data_mtime": **********, "dep_lines": [4, 5, 10, 11, 12, 13, 14, 19, 20, 1, 3, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 17], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 25, 25, 5, 10, 20, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["collections.abc", "http.cookies", "starlette._utils", "starlette.datastructures", "starlette.exceptions", "starlette.formparsers", "starlette.types", "starlette.applications", "starlette.routing", "__future__", "json", "http", "typing", "anyio", "builtins", "_typeshed", "abc", "anyio._core", "anyio._core._tasks", "contextlib", "json.decoder", "typing_extensions"], "hash": "8e2b38b016c4699d661154db9c658e1c3c6cd679b7aab1d9e728b7274cd541da", "id": "starlette.requests", "ignore_all": true, "interface_hash": "50cad2eb8c72aecd1dd9f9c5c96957a65b632ab36c7fbdef0b88c24729485082", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/requests.py", "plugin_data": null, "size": 11683, "suppressed": ["python_multipart.multipart"], "version_id": "1.8.0"}