{"data_mtime": 1753508936, "dep_lines": [5, 8, 9, 10, 1, 3, 4, 6, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 30], "dependencies": ["collections.abc", "starlette.requests", "starlette.responses", "starlette.types", "__future__", "enum", "json", "typing", "builtins", "abc"], "hash": "a61b168295dc958ade56183ec00c945a98015894e26cd1793e2fad3716e64056", "id": "starlette.websockets", "ignore_all": true, "interface_hash": "7b06a6b8f1342c216365be9e801982820e7341e8800d25acda4194973641ba49", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/websockets.py", "plugin_data": null, "size": 8336, "suppressed": [], "version_id": "1.8.0"}