{"data_mtime": 1753508936, "dep_lines": [4, 12, 13, 1, 3, 5, 10, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "starlette._utils", "starlette.concurrency", "__future__", "sys", "typing", "typing_extensions", "builtins", "_typeshed", "abc"], "hash": "d31767fd04e7732c7dbd7e8c15d3dc61bbfced7f9b8598dc016cb638b7559ce5", "id": "starlette.background", "ignore_all": true, "interface_hash": "1020fd8fa4671cc2f770315a8555e34b8b4daf66f7dda327631fee807cfcdbd1", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/starlette/background.py", "plugin_data": null, "size": 1278, "suppressed": [], "version_id": "1.8.0"}