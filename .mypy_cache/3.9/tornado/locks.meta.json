{"data_mtime": 1753508937, "dep_lines": [19, 19, 20, 15, 16, 17, 19, 22, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 20, 5, 5, 30, 30, 30], "dependencies": ["tornado.gen", "tornado.ioloop", "tornado.concurrent", "collections", "datetime", "types", "tornado", "typing", "builtins", "abc", "asyncio", "asyncio.futures"], "hash": "3bff7b17678f84864c252e48ecdb5d4176f3657535a0facb2196c4507aeead14", "id": "tornado.locks", "ignore_all": true, "interface_hash": "4b161718dd397383394fa11e3cafa975cdb1c20e50e7d2a6d0467d1dbc08c0f2", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/locks.py", "plugin_data": null, "size": 17260, "suppressed": [], "version_id": "1.8.0"}