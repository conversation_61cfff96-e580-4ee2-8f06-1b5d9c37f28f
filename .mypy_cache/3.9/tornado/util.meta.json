{"data_mtime": 1753508935, "dep_lines": [434, 13, 14, 15, 16, 17, 18, 19, 37, 38, 40, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 10, 5, 10, 25, 25, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["tornado.speedups", "array", "asyncio", "inspect", "os", "re", "typing", "zlib", "datetime", "types", "unittest", "builtins", "abc", "asyncio.exceptions", "enum", "typing_extensions", "unittest.suite"], "hash": "386a749c39111b82995878a6b92f469bc1a9ebeb5ca97dda54259ab058a88c2b", "id": "tornado.util", "ignore_all": true, "interface_hash": "234738d84baa1eec02fef2c11eab36c81e3029a4e0163c43396e44d7bdc8afa6", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/util.py", "plugin_data": null, "size": 15780, "suppressed": [], "version_id": "1.8.0"}