{".class": "MypyFile", "_fullname": "tornado.locale", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CONTEXT_SEPARATOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.locale.CONTEXT_SEPARATOR", "name": "CONTEXT_SEPARATOR", "type": "builtins.str"}}, "CSVLocale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.locale.Locale"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.locale.CSVLocale", "name": "CSVLocale", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.locale.CSVLocale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.locale", "mro": ["tornado.locale.CSVLocale", "tornado.locale.Locale", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "translations"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.CSVLocale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "translations"], "arg_types": ["tornado.locale.CSVLocale", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CSVLocale", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pgettext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "context", "message", "plural_message", "count"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.CSVLocale.pgettext", "name": "pgettext", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "context", "message", "plural_message", "count"], "arg_types": ["tornado.locale.CSVLocale", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pgettext of CSVLocale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "translate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "plural_message", "count"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.CSVLocale.translate", "name": "translate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "plural_message", "count"], "arg_types": ["tornado.locale.CSVLocale", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate of CSVLocale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "translations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.locale.CSVLocale.translations", "name": "translations", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}], "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.locale.CSVLocale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.locale.CSVLocale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GettextLocale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.locale.Locale"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.locale.GettextLocale", "name": "GettextLocale", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.locale.GettextLocale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.locale", "mro": ["tornado.locale.GettextLocale", "tornado.locale.Locale", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "translations"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.GettextLocale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "translations"], "arg_types": ["tornado.locale.GettextLocale", "builtins.str", "gettext.NullTranslations"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GettextLocale", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "gettext": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.locale.GettextLocale.gettext", "name": "gettext", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["message"], "arg_types": ["builtins.str"], "bound_args": ["gettext.NullTranslations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ngettext": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.locale.GettextLocale.ngettext", "name": "ngettext", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["msgid1", "msgid2", "n"], "arg_types": ["builtins.str", "builtins.str", "builtins.int"], "bound_args": ["gettext.NullTranslations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pgettext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "context", "message", "plural_message", "count"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.GettextLocale.pgettext", "name": "pgettext", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "context", "message", "plural_message", "count"], "arg_types": ["tornado.locale.GettextLocale", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pgettext of GettextLocale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "translate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "plural_message", "count"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.GettextLocale.translate", "name": "translate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "plural_message", "count"], "arg_types": ["tornado.locale.GettextLocale", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate of GettextLocale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.locale.GettextLocale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.locale.GettextLocale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LOCALE_NAMES": {".class": "SymbolTableNode", "cross_ref": "tornado._locale_data.LOCALE_NAMES", "kind": "Gdef"}, "Locale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.locale.Locale", "name": "Locale", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.locale.Locale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.locale", "mro": ["tornado.locale.Locale", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.Locale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["tornado.locale.Locale", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Locale", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "tornado.locale.Locale._cache", "name": "_cache", "type": {".class": "Instance", "args": ["builtins.str", "tornado.locale.Locale"], "type_ref": "builtins.dict"}}}, "_months": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.locale.Locale._months", "name": "_months", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "type_ref": "builtins.list"}}}, "_weekdays": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.locale.Locale._weekdays", "name": "_weekdays", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "type_ref": "builtins.list"}}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.locale.Locale.code", "name": "code", "type": "builtins.str"}}, "format_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "date", "gmt_offset", "relative", "shorter", "full_format"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.Locale.format_date", "name": "format_date", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "date", "gmt_offset", "relative", "shorter", "full_format"], "arg_types": ["tornado.locale.Locale", {".class": "UnionType", "items": ["builtins.int", "builtins.float", "datetime.datetime"]}, "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_date of Locale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format_day": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "date", "gmt_offset", "dow"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.Locale.format_day", "name": "format_day", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "date", "gmt_offset", "dow"], "arg_types": ["tornado.locale.Locale", "datetime.datetime", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_day of Locale", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "friendly_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.Locale.friendly_number", "name": "friendly_number", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["tornado.locale.Locale", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "friendly_number of Locale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "code"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.locale.Locale.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "code"], "arg_types": [{".class": "TypeType", "item": "tornado.locale.Locale"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Locale", "ret_type": "tornado.locale.Locale", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.locale.Locale.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "code"], "arg_types": [{".class": "TypeType", "item": "tornado.locale.Locale"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Locale", "ret_type": "tornado.locale.Locale", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_closest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["cls", "locale_codes"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.locale.Locale.get_closest", "name": "get_closest", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["cls", "locale_codes"], "arg_types": [{".class": "TypeType", "item": "tornado.locale.Locale"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest of Locale", "ret_type": "tornado.locale.Locale", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.locale.Locale.get_closest", "name": "get_closest", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["cls", "locale_codes"], "arg_types": [{".class": "TypeType", "item": "tornado.locale.Locale"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest of Locale", "ret_type": "tornado.locale.Locale", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parts"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.Locale.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parts"], "arg_types": ["tornado.locale.Locale", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list of Locale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.locale.Locale.name", "name": "name", "type": "builtins.str"}}, "pgettext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "context", "message", "plural_message", "count"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.Locale.pgettext", "name": "pgettext", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "context", "message", "plural_message", "count"], "arg_types": ["tornado.locale.Locale", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pgettext of Locale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rtl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.locale.Locale.rtl", "name": "rtl", "type": "builtins.bool"}}, "translate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "plural_message", "count"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.Locale.translate", "name": "translate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "plural_message", "count"], "arg_types": ["tornado.locale.Locale", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate of Locale", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.locale.Locale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.locale.Locale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.locale.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.locale.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.locale.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.locale.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.locale.__package__", "name": "__package__", "type": "builtins.str"}}, "_default_locale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.locale._default_locale", "name": "_default_locale", "type": "builtins.str"}}, "_supported_locales": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.locale._supported_locales", "name": "_supported_locales", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.frozenset"}}}, "_translations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "tornado.locale._translations", "name": "_translations", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "_use_gettext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.locale._use_gettext", "name": "_use_gettext", "type": "builtins.bool"}}, "codecs": {".class": "SymbolTableNode", "cross_ref": "codecs", "kind": "Gdef"}, "csv": {".class": "SymbolTableNode", "cross_ref": "csv", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "escape": {".class": "SymbolTableNode", "cross_ref": "tornado.escape", "kind": "Gdef"}, "gen_log": {".class": "SymbolTableNode", "cross_ref": "tornado.log.gen_log", "kind": "Gdef"}, "get": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["locale_codes"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["locale_codes"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get", "ret_type": "tornado.locale.Locale", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_supported_locales": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.get_supported_locales", "name": "get_supported_locales", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_locales", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "gettext": {".class": "SymbolTableNode", "cross_ref": "gettext", "kind": "Gdef"}, "glob": {".class": "SymbolTableNode", "cross_ref": "glob", "kind": "Gdef"}, "load_gettext_translations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["directory", "domain"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.load_gettext_translations", "name": "load_gettext_translations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["directory", "domain"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_gettext_translations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_translations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["directory", "encoding"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.load_translations", "name": "load_translations", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["directory", "encoding"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_translations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "set_default_locale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["code"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.locale.set_default_locale", "name": "set_default_locale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["code"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_locale", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/locale.py"}