{".class": "MypyFile", "_fullname": "tornado", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "auth": {".class": "SymbolTableNode", "cross_ref": "tornado.auth", "kind": "Gdef", "module_public": false}, "autoreload": {".class": "SymbolTableNode", "cross_ref": "tornado.autoreload", "kind": "Gdef", "module_public": false}, "concurrent": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent", "kind": "Gdef", "module_public": false}, "curl_httpclient": {".class": "SymbolTableNode", "cross_ref": "tornado.curl_httpclient", "kind": "Gdef", "module_public": false}, "escape": {".class": "SymbolTableNode", "cross_ref": "tornado.escape", "kind": "Gdef", "module_public": false}, "gen": {".class": "SymbolTableNode", "cross_ref": "tornado.gen", "kind": "Gdef", "module_public": false}, "http1connection": {".class": "SymbolTableNode", "cross_ref": "tornado.http1connection", "kind": "Gdef", "module_public": false}, "httpclient": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient", "kind": "Gdef", "module_public": false}, "httpserver": {".class": "SymbolTableNode", "cross_ref": "tornado.httpserver", "kind": "Gdef", "module_public": false}, "httputil": {".class": "SymbolTableNode", "cross_ref": "tornado.httputil", "kind": "Gdef", "module_public": false}, "ioloop": {".class": "SymbolTableNode", "cross_ref": "tornado.ioloop", "kind": "Gdef", "module_public": false}, "iostream": {".class": "SymbolTableNode", "cross_ref": "tornado.iostream", "kind": "Gdef", "module_public": false}, "locale": {".class": "SymbolTableNode", "cross_ref": "tornado.locale", "kind": "Gdef", "module_public": false}, "locks": {".class": "SymbolTableNode", "cross_ref": "tornado.locks", "kind": "Gdef", "module_public": false}, "log": {".class": "SymbolTableNode", "cross_ref": "tornado.log", "kind": "Gdef", "module_public": false}, "netutil": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil", "kind": "Gdef", "module_public": false}, "options": {".class": "SymbolTableNode", "cross_ref": "tornado.options", "kind": "Gdef", "module_public": false}, "platform": {".class": "SymbolTableNode", "cross_ref": "tornado.platform", "kind": "Gdef", "module_public": false}, "process": {".class": "SymbolTableNode", "cross_ref": "tornado.process", "kind": "Gdef", "module_public": false}, "queues": {".class": "SymbolTableNode", "cross_ref": "tornado.queues", "kind": "Gdef", "module_public": false}, "routing": {".class": "SymbolTableNode", "cross_ref": "tornado.routing", "kind": "Gdef", "module_public": false}, "simple_httpclient": {".class": "SymbolTableNode", "cross_ref": "tornado.simple_httpclient", "kind": "Gdef", "module_public": false}, "tcpclient": {".class": "SymbolTableNode", "cross_ref": "tornado.tcpclient", "kind": "Gdef", "module_public": false}, "tcpserver": {".class": "SymbolTableNode", "cross_ref": "tornado.tcpserver", "kind": "Gdef", "module_public": false}, "template": {".class": "SymbolTableNode", "cross_ref": "tornado.template", "kind": "Gdef", "module_public": false}, "testing": {".class": "SymbolTableNode", "cross_ref": "tornado.testing", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "util": {".class": "SymbolTableNode", "cross_ref": "tornado.util", "kind": "Gdef", "module_public": false}, "version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.version", "name": "version", "type": "builtins.str"}}, "version_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.version_info", "name": "version_info", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "web": {".class": "SymbolTableNode", "cross_ref": "tornado.web", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/__init__.pyi"}