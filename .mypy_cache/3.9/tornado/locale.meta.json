{"data_mtime": 1753508937, "dep_lines": [48, 49, 51, 40, 41, 42, 43, 44, 45, 46, 48, 53, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 20, 5, 5, 30, 30], "dependencies": ["tornado.escape", "tornado.log", "tornado._locale_data", "codecs", "csv", "datetime", "gettext", "glob", "os", "re", "tornado", "typing", "builtins", "abc", "typing_extensions"], "hash": "95a7f71cb3cf7b02ed96fff2099d368a0d44b1642205da98de5cec1b35c3a4f6", "id": "tornado.locale", "ignore_all": true, "interface_hash": "6e0b01ddf1558935e789cd639a82c0fdd05ea7cfd8b77d297e44b9ef6fc21a37", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/locale.py", "plugin_data": null, "size": 21122, "suppressed": [], "version_id": "1.8.0"}