{"data_mtime": 1753508937, "dep_lines": [32, 32, 33, 34, 28, 29, 30, 32, 36, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 10, 10, 20, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["tornado.gen", "tornado.ioloop", "tornado.concurrent", "tornado.locks", "collections", "datetime", "heapq", "tornado", "typing", "builtins", "abc", "anyio", "anyio._core", "anyio._core._synchronization", "asyncio", "asyncio.futures"], "hash": "224d6f5d6c7e444d77d106bc64bed7a95194f1c7743670cc8ee0cd170a4fcd57", "id": "tornado.queues", "ignore_all": true, "interface_hash": "bd4bbf979c54565da7c9761af9e20e977360d5b35bd2ebafff28ec221ea2bf8f", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/queues.py", "plugin_data": null, "size": 12513, "suppressed": [], "version_id": "1.8.0"}