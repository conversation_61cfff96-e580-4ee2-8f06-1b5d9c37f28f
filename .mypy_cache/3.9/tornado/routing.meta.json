{"data_mtime": **********, "dep_lines": [181, 182, 183, 184, 185, 178, 179, 181, 187, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 20, 5, 5, 30, 30, 30], "dependencies": ["tornado.httputil", "tornado.httpserver", "tornado.escape", "tornado.log", "tornado.util", "re", "functools", "tornado", "typing", "builtins", "abc", "logging", "typing_extensions"], "hash": "2bacbb8c485d95673272a0b2379c1defba6f44356994e91e035fc200d711bab7", "id": "tornado.routing", "ignore_all": true, "interface_hash": "9d0e4b5f949e8820f738edaa173b1dce5d52ad227c6f8c822b8f611cb3ececd2", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/routing.py", "plugin_data": null, "size": 25139, "suppressed": [], "version_id": "1.8.0"}