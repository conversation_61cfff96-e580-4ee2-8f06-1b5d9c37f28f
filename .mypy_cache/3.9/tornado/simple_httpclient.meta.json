{"data_mtime": 1753508937, "dep_lines": [1, 2, 3, 11, 12, 13, 14, 15, 21, 22, 34, 2, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tornado.escape", "tornado.gen", "tornado.httpclient", "tornado.httputil", "tornado.http1connection", "tornado.ioloop", "tornado.iostream", "tornado.netutil", "tornado.log", "tornado.tcpclient", "urllib.parse", "tornado", "base64", "collections", "copy", "functools", "re", "socket", "ssl", "sys", "time", "io", "urllib", "typing", "types", "builtins", "_typeshed", "abc", "ast", "asyncio", "asyncio.futures", "concurrent", "concurrent.futures", "concurrent.futures._base", "<PERSON><PERSON><PERSON>", "datetime", "enum", "os", "tornado.util", "typing_extensions"], "hash": "153448e9fb407687346c39844d7e3f15eb7d8e82f2c6f96a80230bc58d75a2d2", "id": "tornado.simple_httpclient", "ignore_all": true, "interface_hash": "343983cfd2e91a44be8bf6f47b70609c73f86897734ba2682c2392fabe098314", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/simple_httpclient.py", "plugin_data": null, "size": 27747, "suppressed": [], "version_id": "1.8.0"}