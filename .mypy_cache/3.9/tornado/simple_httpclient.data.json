{".class": "MypyFile", "_fullname": "tornado.simple_httpclient", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"HTTPRequest\" and \"_RequestProxy\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.HTTPRequest", "tornado.httpclient._RequestProxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.simple_httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "name": "<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "tornado.simple_httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.simple_httpclient", "mro": ["tornado.simple_httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "tornado.httpclient.HTTPRequest", "tornado.httpclient._RequestProxy", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"HTTPRequest\" and \"_RequestProxy\">1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.HTTPRequest", "tornado.httpclient._RequestProxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.simple_httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">1", "name": "<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "tornado.simple_httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.simple_httpclient", "mro": ["tornado.simple_httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">1", "tornado.httpclient.HTTPRequest", "tornado.httpclient._RequestProxy", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncHTTPClient": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.AsyncHTTPClient", "kind": "Gdef"}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "io.BytesIO", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HTTP1Connection": {".class": "SymbolTableNode", "cross_ref": "tornado.http1connection.HTTP1Connection", "kind": "Gdef"}, "HTTP1ConnectionParameters": {".class": "SymbolTableNode", "cross_ref": "tornado.http1connection.HTTP1ConnectionParameters", "kind": "Gdef"}, "HTTPError": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.HTTPError", "kind": "Gdef"}, "HTTPRequest": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.HTTPRequest", "kind": "Gdef"}, "HTTPResponse": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.HTTPResponse", "kind": "Gdef"}, "HTTPStreamClosedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.simple_httpclient.HTTPStreamClosedError", "name": "HTTPStreamClosedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.simple_httpclient.HTTPStreamClosedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.simple_httpclient", "mro": ["tornado.simple_httpclient.HTTPStreamClosedError", "tornado.httpclient.HTTPClientError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.HTTPStreamClosedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["tornado.simple_httpclient.HTTPStreamClosedError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPStreamClosedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.HTTPStreamClosedError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.simple_httpclient.HTTPStreamClosedError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of HTTPStreamClosedError", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.simple_httpclient.HTTPStreamClosedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.simple_httpclient.HTTPStreamClosedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPTimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.simple_httpclient.HTTPTimeoutError", "name": "HTTPTimeoutError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.simple_httpclient.HTTPTimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.simple_httpclient", "mro": ["tornado.simple_httpclient.HTTPTimeoutError", "tornado.httpclient.HTTPClientError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.HTTPTimeoutError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["tornado.simple_httpclient.HTTPTimeoutError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPTimeoutError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.HTTPTimeoutError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.simple_httpclient.HTTPTimeoutError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of HTTPTimeoutError", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.simple_httpclient.HTTPTimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.simple_httpclient.HTTPTimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IOLoop": {".class": "SymbolTableNode", "cross_ref": "tornado.ioloop.IOLoop", "kind": "Gdef"}, "IOStream": {".class": "SymbolTableNode", "cross_ref": "tornado.iostream.IOStream", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OverrideResolver": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil.OverrideResolver", "kind": "Gdef"}, "Resolver": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil.Resolver", "kind": "Gdef"}, "SimpleAsyncHTTPClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.AsyncHTTPClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient", "name": "SimpleAsyncHTTPClient", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.simple_httpclient", "mro": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", "tornado.httpclient.AsyncHTTPClient", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "_connection_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient._connection_class", "name": "_connection_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connection_class of SimpleAsyncHTTPClient", "ret_type": "builtins.type", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_handle_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "release_callback", "final_callback"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient._handle_request", "name": "_handle_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "release_callback", "final_callback"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", "tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_request of SimpleAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_on_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "info"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient._on_timeout", "name": "_on_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "info"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", "builtins.object", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_timeout of SimpleAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_process_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient._process_queue", "name": "_process_queue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_queue of SimpleAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_release_fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient._release_fetch", "name": "_release_fetch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_release_fetch of SimpleAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_remove_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient._remove_timeout", "name": "_remove_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_timeout of SimpleAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "active": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.active", "name": "active", "type": {".class": "Instance", "args": ["builtins.object", {".class": "TupleType", "implicit": false, "items": ["tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.dict"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of SimpleAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fetch_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "callback"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.fetch_impl", "name": "fetch_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "callback"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", "tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_impl of SimpleAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "max_clients", "hostname_mapping", "max_buffer_size", "resolver", "defaults", "max_header_size", "max_body_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "max_clients", "hostname_mapping", "max_buffer_size", "resolver", "defaults", "max_header_size", "max_body_size"], "arg_types": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, "builtins.int", {".class": "UnionType", "items": ["tornado.netutil.Resolver", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of SimpleAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "max_body_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.max_body_size", "name": "max_body_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "max_buffer_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.max_buffer_size", "name": "max_buffer_size", "type": "builtins.int"}}, "max_clients": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.max_clients", "name": "max_clients", "type": "builtins.int"}}, "max_header_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.max_header_size", "name": "max_header_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "own_resolver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.own_resolver", "name": "own_resolver", "type": "builtins.bool"}}, "queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.queue", "name": "queue", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.object", "tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "collections.deque"}}}, "resolver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.resolver", "name": "resolver", "type": "tornado.netutil.Resolver"}}, "tcp_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.tcp_client", "name": "tcp_client", "type": "tornado.tcpclient.TCPClient"}}, "waiting": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.waiting", "name": "waiting", "type": {".class": "Instance", "args": ["builtins.object", {".class": "TupleType", "implicit": false, "items": ["tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.simple_httpclient.SimpleAsyncHTTPClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.simple_httpclient.SimpleAsyncHTTPClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamClosedError": {".class": "SymbolTableNode", "cross_ref": "tornado.iostream.StreamClosedError", "kind": "Gdef"}, "TCPClient": {".class": "SymbolTableNode", "cross_ref": "tornado.tcpclient.TCPClient", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_HTTPConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httputil.HTTPMessageDelegate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.simple_httpclient._HTTPConnection", "name": "_HTTPConnection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.simple_httpclient", "mro": ["tornado.simple_httpclient._HTTPConnection", "tornado.httputil.HTTPMessageDelegate", "builtins.object"], "names": {".class": "SymbolTable", "_SUPPORTED_METHODS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "tornado.simple_httpclient._HTTPConnection._SUPPORTED_METHODS", "name": "_SUPPORTED_METHODS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "client", "request", "release_callback", "final_callback", "max_buffer_size", "tcp_client", "max_header_size", "max_body_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "client", "request", "release_callback", "final_callback", "max_buffer_size", "tcp_client", "max_header_size", "max_body_size"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", {".class": "UnionType", "items": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", {".class": "NoneType"}]}, "tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "tornado.tcpclient.TCPClient", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_create_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._create_connection", "name": "_create_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", "tornado.iostream.IOStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_connection of _HTTPConnection", "ret_type": "tornado.http1connection.HTTP1Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_decompressor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection._decompressor", "name": "_decompressor", "type": {".class": "NoneType"}}}, "_get_ssl_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scheme"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._get_ssl_options", "name": "_get_ssl_options", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scheme"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ssl_options of _HTTPConnection", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "ssl.SSLContext"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_handle_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "typ", "value", "tb"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._handle_exception", "name": "_handle_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "typ", "value", "tb"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_exception of _HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_on_end_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._on_end_request", "name": "_on_end_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient._HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_end_request of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_on_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "info"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._on_timeout", "name": "_on_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "info"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_timeout of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._release", "name": "_release", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient._HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_release of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_remove_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._remove_timeout", "name": "_remove_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient._HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_timeout of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_run_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._run_callback", "name": "_run_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", "tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_callback of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_should_follow_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection._should_follow_redirect", "name": "_should_follow_redirect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient._HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_follow_redirect of _HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_sockaddr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection._sockaddr", "name": "_sockaddr", "type": {".class": "NoneType"}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection._timeout", "name": "_timeout", "type": "builtins.object"}}, "_write_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_read"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.simple_httpclient._HTTPConnection._write_body", "name": "_write_body", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "start_read"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_write_body of _HTTPConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "chunks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.chunks", "name": "chunks", "type": {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "builtins.list"}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.client", "name": "client", "type": {".class": "UnionType", "items": ["tornado.simple_httpclient.SimpleAsyncHTTPClient", {".class": "NoneType"}]}}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.code", "name": "code", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.connection", "name": "connection", "type": "tornado.http1connection.HTTP1Connection"}}, "data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection.data_received", "name": "data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_received of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "final_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.final_callback", "name": "final_callback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "finish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection.finish", "name": "finish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient._HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finish of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.headers", "name": "headers", "type": {".class": "UnionType", "items": ["tornado.httputil.HTTPHeaders", {".class": "NoneType"}]}}}, "headers_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "first_line", "headers"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.simple_httpclient._HTTPConnection.headers_received", "name": "headers_received", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "first_line", "headers"], "arg_types": ["tornado.simple_httpclient._HTTPConnection", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.ResponseStartLine"}, {".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.RequestStartLine"}]}, "tornado.httputil.HTTPHeaders"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers_received of _HTTPConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "io_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.io_loop", "name": "io_loop", "type": "tornado.ioloop.IOLoop"}}, "max_body_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.max_body_size", "name": "max_body_size", "type": "builtins.int"}}, "max_buffer_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.max_buffer_size", "name": "max_buffer_size", "type": "builtins.int"}}, "max_header_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.max_header_size", "name": "max_header_size", "type": "builtins.int"}}, "on_connection_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.simple_httpclient._HTTPConnection.on_connection_close", "name": "on_connection_close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient._HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_close of _HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parsed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.parsed", "name": "parsed", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "urllib.parse.SplitResult"}}}, "parsed_hostname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.parsed_hostname", "name": "parsed_hostname", "type": "builtins.str"}}, "reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.reason", "name": "reason", "type": "builtins.str"}}, "release_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.release_callback", "name": "release_callback", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.request", "name": "request", "type": "tornado.httpclient.HTTPRequest"}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.simple_httpclient._HTTPConnection.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.simple_httpclient._HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _HTTPConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "start_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.start_time", "name": "start_time", "type": "builtins.float"}}, "start_wall_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.start_wall_time", "name": "start_wall_time", "type": "builtins.float"}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.stream", "name": "stream", "type": "tornado.iostream.IOStream"}}, "tcp_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.simple_httpclient._HTTPConnection.tcp_client", "name": "tcp_client", "type": "tornado.tcpclient.TCPClient"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.simple_httpclient._HTTPConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.simple_httpclient._HTTPConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RequestProxy": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient._RequestProxy", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.simple_httpclient.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.simple_httpclient.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.simple_httpclient.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.simple_httpclient.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.simple_httpclient.__package__", "name": "__package__", "type": "builtins.str"}}, "_client_ssl_defaults": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil._client_ssl_defaults", "kind": "Gdef"}, "_unicode": {".class": "SymbolTableNode", "cross_ref": "tornado.escape._unicode", "kind": "Gdef"}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "gen": {".class": "SymbolTableNode", "cross_ref": "tornado.gen", "kind": "Gdef"}, "gen_log": {".class": "SymbolTableNode", "cross_ref": "tornado.log.gen_log", "kind": "Gdef"}, "httputil": {".class": "SymbolTableNode", "cross_ref": "tornado.httputil", "kind": "Gdef"}, "is_valid_ip": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil.is_valid_ip", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.main", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "tornado.version", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/simple_httpclient.py"}