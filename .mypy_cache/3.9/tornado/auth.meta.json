{"data_mtime": 1753508937, "dep_lines": [77, 81, 82, 83, 84, 85, 72, 73, 74, 75, 76, 77, 78, 79, 81, 87, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 10, 10, 10, 10, 10, 20, 10, 10, 20, 5, 5, 30], "dependencies": ["urllib.parse", "tornado.httpclient", "tornado.escape", "tornado.httputil", "tornado.util", "tornado.web", "base64", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "hmac", "time", "urllib", "uuid", "warnings", "tornado", "typing", "builtins", "abc"], "hash": "3d0d2a2f77d6dd39dab9601a206ee7a6b38a54f32f40a36abc210dd21cce7eab", "id": "tornado.auth", "ignore_all": true, "interface_hash": "cc534744a67868fa1a9a4cb5dcc46cc5136927aaa421580b1650097847af578a", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/auth.py", "plugin_data": null, "size": 48955, "suppressed": [], "version_id": "1.8.0"}