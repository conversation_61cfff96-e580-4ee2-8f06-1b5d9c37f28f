{"data_mtime": 1753508937, "dep_lines": [67, 68, 76, 84, 85, 86, 64, 65, 66, 68, 69, 70, 72, 73, 74, 89, 93, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 10, 10, 10, 20, 10, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "concurrent.futures", "tornado.concurrent", "tornado.ioloop", "tornado.log", "tornado.util", "asyncio", "builtins", "collections", "concurrent", "datetime", "functools", "inspect", "sys", "types", "<PERSON><PERSON><PERSON>", "typing", "_collections_abc", "abc", "asyncio.events", "asyncio.futures", "concurrent.futures._base"], "hash": "8169fe7326ed8d746682f3e9fb691dbd19b81781b1762ec272af5a53e2504661", "id": "tornado.gen", "ignore_all": true, "interface_hash": "9b625fbd9fd437cf7f9f33fda78ec301d517973491719be6daa57d9a286a6734", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/gen.py", "plugin_data": null, "size": 31435, "suppressed": [], "version_id": "1.8.0"}