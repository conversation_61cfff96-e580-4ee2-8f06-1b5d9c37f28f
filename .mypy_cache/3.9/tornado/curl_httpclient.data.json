{".class": "MypyFile", "_fullname": "tornado.curl_httpclient", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncHTTPClient": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.AsyncHTTPClient", "kind": "Gdef"}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "io.BytesIO", "kind": "Gdef"}, "CR_OR_LF_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.curl_httpclient.CR_OR_LF_RE", "name": "CR_OR_LF_RE", "type": {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "<PERSON><PERSON>"}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CurlAsyncHTTPClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.AsyncHTTPClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient", "name": "CurlAsyncHTTPClient", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.curl_httpclient", "mro": ["tornado.curl_httpclient.CurlAsyncHTTPClient", "tornado.httpclient.AsyncHTTPClient", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "_curl_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._curl_create", "name": "_curl_create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_curl_create of CurlAsyncHTTPClient", "ret_type": {".class": "AnyType", "missing_import_name": "tornado.curl_httpclient.pycurl", "source_any": null, "type_of_any": 3}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_curl_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "debug_type", "debug_msg"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._curl_debug", "name": "_curl_debug", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "debug_type", "debug_msg"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_curl_debug of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_curl_header_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "headers", "header_callback", "header_line_bytes"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._curl_header_callback", "name": "_curl_header_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "headers", "header_callback", "header_line_bytes"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", "tornado.httputil.HTTPHeaders", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_curl_header_callback of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_curl_setup_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "curl", "request", "buffer", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._curl_setup_request", "name": "_curl_setup_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "curl", "request", "buffer", "headers"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", {".class": "AnyType", "missing_import_name": "tornado.curl_httpclient.pycurl", "source_any": null, "type_of_any": 3}, "tornado.httpclient.HTTPRequest", "io.BytesIO", "tornado.httputil.HTTPHeaders"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_curl_setup_request of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_curls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._curls", "name": "_curls", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "tornado.curl_httpclient.pycurl", "source_any": {".class": "AnyType", "missing_import_name": "tornado.curl_httpclient.pycurl", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "type_ref": "builtins.list"}}}, "_fds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._fds", "name": "_fds", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "type_ref": "builtins.dict"}}}, "_finish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "curl", "curl_error", "curl_message"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._finish", "name": "_finish", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "curl", "curl_error", "curl_message"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", {".class": "AnyType", "missing_import_name": "tornado.curl_httpclient.pycurl", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finish of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_finish_pending_requests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._finish_pending_requests", "name": "_finish_pending_requests", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finish_pending_requests of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_force_timeout_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._force_timeout_callback", "name": "_force_timeout_callback", "type": "tornado.ioloop.PeriodicCallback"}}, "_free_list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._free_list", "name": "_free_list", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "tornado.curl_httpclient.pycurl", "source_any": {".class": "AnyType", "missing_import_name": "tornado.curl_httpclient.pycurl", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "type_ref": "builtins.list"}}}, "_handle_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fd", "events"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._handle_events", "name": "_handle_events", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fd", "events"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_events of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_handle_force_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._handle_force_timeout", "name": "_handle_force_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_force_timeout of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_handle_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "event", "fd", "multi", "data"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._handle_socket", "name": "_handle_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "event", "fd", "multi", "data"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_socket of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_handle_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._handle_timeout", "name": "_handle_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_timeout of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_multi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._multi", "name": "_multi", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_process_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._process_queue", "name": "_process_queue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_queue of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_requests": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._requests", "name": "_requests", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "collections.deque"}}}, "_set_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msecs"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._set_timeout", "name": "_set_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msecs"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_timeout of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient._timeout", "name": "_timeout", "type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fetch_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "callback"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient.fetch_impl", "name": "fetch_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "callback"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", "tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_impl of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handle_callback_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient.handle_callback_exception", "name": "handle_callback_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_callback_exception of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "max_clients", "defaults"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "max_clients", "defaults"], "arg_types": ["tornado.curl_httpclient.CurlAsyncHTTPClient", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of CurlAsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.curl_httpclient.CurlAsyncHTTPClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.curl_httpclient.CurlAsyncHTTPClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CurlError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.curl_httpclient.CurlError", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.curl_httpclient.CurlError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.curl_httpclient", "mro": ["tornado.curl_httpclient.CurlError", "tornado.httpclient.HTTPClientError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "errno", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.curl_httpclient.CurlError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "errno", "message"], "arg_types": ["tornado.curl_httpclient.CurlError", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CurlError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "errno": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.curl_httpclient.CurlError.errno", "name": "errno", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.curl_httpclient.CurlError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.curl_httpclient.CurlError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HTTPError": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.HTTPError", "kind": "Gdef"}, "HTTPRequest": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.HTTPRequest", "kind": "Gdef"}, "HTTPResponse": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.HTTPResponse", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.curl_httpclient.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.curl_httpclient.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.curl_httpclient.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.curl_httpclient.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.curl_httpclient.__package__", "name": "__package__", "type": "builtins.str"}}, "app_log": {".class": "SymbolTableNode", "cross_ref": "tornado.log.app_log", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "curl_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.curl_httpclient.curl_log", "name": "curl_log", "type": "logging.Logger"}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "httputil": {".class": "SymbolTableNode", "cross_ref": "tornado.httputil", "kind": "Gdef"}, "ioloop": {".class": "SymbolTableNode", "cross_ref": "tornado.ioloop", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient.main", "kind": "Gdef"}, "native_str": {".class": "SymbolTableNode", "cross_ref": "tornado.escape.native_str", "kind": "Gdef"}, "pycurl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tornado.curl_httpclient.pycurl", "name": "pycurl", "type": {".class": "AnyType", "missing_import_name": "tornado.curl_httpclient.pycurl", "source_any": null, "type_of_any": 3}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "utf8": {".class": "SymbolTableNode", "cross_ref": "tornado.escape.utf8", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/curl_httpclient.py"}