{".class": "MypyFile", "_fullname": "tornado.http1connection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "CR_OR_LF_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.http1connection.CR_OR_LF_RE", "name": "CR_OR_LF_RE", "type": {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "<PERSON><PERSON>"}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DIGITS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.http1connection.DIGITS", "name": "DIGITS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "Future": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent.Future", "kind": "Gdef"}, "GzipDecompressor": {".class": "SymbolTableNode", "cross_ref": "tornado.util.GzipDecompressor", "kind": "Gdef"}, "HEXDIGITS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.http1connection.HEXDIGITS", "name": "HEXDIGITS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "HTTP1Connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httputil.HTTPConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.http1connection.HTTP1Connection", "name": "HTTP1Connection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.http1connection.HTTP1Connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.http1connection", "mro": ["tornado.http1connection.HTTP1Connection", "tornado.httputil.HTTPConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "stream", "is_client", "params", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "stream", "is_client", "params", "context"], "arg_types": ["tornado.http1connection.HTTP1Connection", "tornado.iostream.IOStream", "builtins.bool", {".class": "UnionType", "items": ["tornado.http1connection.HTTP1ConnectionParameters", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_body_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._body_timeout", "name": "_body_timeout", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}}}, "_can_keep_alive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_line", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection._can_keep_alive", "name": "_can_keep_alive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_line", "headers"], "arg_types": ["tornado.http1connection.HTTP1Connection", {".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.RequestStartLine"}, "tornado.httputil.HTTPHeaders"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_can_keep_alive of HTTP1Connection", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_chunking_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._chunking_output", "name": "_chunking_output", "type": "builtins.bool"}}, "_clear_callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection._clear_callbacks", "name": "_clear_callbacks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection.HTTP1Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear_callbacks of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_close_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._close_callback", "name": "_close_callback", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "_disconnect_on_finish": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._disconnect_on_finish", "name": "_disconnect_on_finish", "type": "builtins.bool"}}, "_expected_content_remaining": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._expected_content_remaining", "name": "_expected_content_remaining", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "_finish_future": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._finish_future", "name": "_finish_future", "type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}}}, "_finish_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "future"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection._finish_request", "name": "_finish_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "future"], "arg_types": ["tornado.http1connection.HTTP1Connection", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finish_request of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_format_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection._format_chunk", "name": "_format_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": ["tornado.http1connection.HTTP1Connection", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_chunk of HTTP1Connection", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_max_body_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._max_body_size", "name": "_max_body_size", "type": "builtins.int"}}, "_on_connection_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection._on_connection_close", "name": "_on_connection_close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection.HTTP1Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_connection_close of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_on_write_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "future"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection._on_write_complete", "name": "_on_write_complete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "future"], "arg_types": ["tornado.http1connection.HTTP1Connection", {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_write_complete of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_parse_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection._parse_headers", "name": "_parse_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["tornado.http1connection.HTTP1Connection", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_headers of HTTP1Connection", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "tornado.httputil.HTTPHeaders"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_pending_write": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._pending_write", "name": "_pending_write", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}, {".class": "NoneType"}]}}}, "_read_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "code", "headers", "delegate"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection._read_body", "name": "_read_body", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "code", "headers", "delegate"], "arg_types": ["tornado.http1connection.HTTP1Connection", "builtins.int", "tornado.httputil.HTTPHeaders", "tornado.httputil.HTTPMessageDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_body of HTTP1Connection", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "typing.Awaitable"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_read_body_until_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.http1connection.HTTP1Connection._read_body_until_close", "name": "_read_body_until_close", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "arg_types": ["tornado.http1connection.HTTP1Connection", "tornado.httputil.HTTPMessageDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_body_until_close of HTTP1Connection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_read_chunked_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.http1connection.HTTP1Connection._read_chunked_body", "name": "_read_chunked_body", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "arg_types": ["tornado.http1connection.HTTP1Connection", "tornado.httputil.HTTPMessageDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_chunked_body of HTTP1Connection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_read_finished": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._read_finished", "name": "_read_finished", "type": "builtins.bool"}}, "_read_fixed_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content_length", "delegate"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.http1connection.HTTP1Connection._read_fixed_body", "name": "_read_fixed_body", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content_length", "delegate"], "arg_types": ["tornado.http1connection.HTTP1Connection", "builtins.int", "tornado.httputil.HTTPMessageDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_fixed_body of HTTP1Connection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_read_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.http1connection.HTTP1Connection._read_message", "name": "_read_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "arg_types": ["tornado.http1connection.HTTP1Connection", "tornado.httputil.HTTPMessageDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_message of HTTP1Connection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_request_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._request_headers", "name": "_request_headers", "type": {".class": "UnionType", "items": ["tornado.httputil.HTTPHeaders", {".class": "NoneType"}]}}}, "_request_start_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._request_start_line", "name": "_request_start_line", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.RequestStartLine"}, {".class": "NoneType"}]}}}, "_response_start_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._response_start_line", "name": "_response_start_line", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.ResponseStartLine"}, {".class": "NoneType"}]}}}, "_write_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._write_callback", "name": "_write_callback", "type": {".class": "NoneType"}}}, "_write_finished": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._write_finished", "name": "_write_finished", "type": "builtins.bool"}}, "_write_future": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection._write_future", "name": "_write_future", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}, {".class": "NoneType"}]}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection.HTTP1Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection.context", "name": "context", "type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}}}, "detach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.detach", "name": "detach", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection.HTTP1Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach of HTTP1Connection", "ret_type": "tornado.iostream.IOStream", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "finish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.finish", "name": "finish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection.HTTP1Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finish of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection.is_client", "name": "is_client", "type": "builtins.bool"}}, "no_keep_alive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection.no_keep_alive", "name": "no_keep_alive", "type": "builtins.bool"}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection.params", "name": "params", "type": "tornado.http1connection.HTTP1ConnectionParameters"}}, "read_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.read_response", "name": "read_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "arg_types": ["tornado.http1connection.HTTP1Connection", "tornado.httputil.HTTPMessageDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_response of HTTP1Connection", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_body_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.set_body_timeout", "name": "set_body_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "arg_types": ["tornado.http1connection.HTTP1Connection", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_body_timeout of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_close_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.set_close_callback", "name": "set_close_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "arg_types": ["tornado.http1connection.HTTP1Connection", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_close_callback of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_max_body_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_body_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.set_max_body_size", "name": "set_max_body_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_body_size"], "arg_types": ["tornado.http1connection.HTTP1Connection", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_max_body_size of HTTP1Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1Connection.stream", "name": "stream", "type": "tornado.iostream.IOStream"}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": ["tornado.http1connection.HTTP1Connection", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of HTTP1Connection", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "write_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "start_line", "headers", "chunk"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1Connection.write_headers", "name": "write_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "start_line", "headers", "chunk"], "arg_types": ["tornado.http1connection.HTTP1Connection", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.RequestStartLine"}, {".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.ResponseStartLine"}]}, "tornado.httputil.HTTPHeaders", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_headers of HTTP1Connection", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.http1connection.HTTP1Connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.http1connection.HTTP1Connection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTP1ConnectionParameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.http1connection.HTTP1ConnectionParameters", "name": "HTTP1ConnectionParameters", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.http1connection.HTTP1ConnectionParameters", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.http1connection", "mro": ["tornado.http1connection.HTTP1ConnectionParameters", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "no_keep_alive", "chunk_size", "max_header_size", "header_timeout", "max_body_size", "body_timeout", "decompress"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1ConnectionParameters.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "no_keep_alive", "chunk_size", "max_header_size", "header_timeout", "max_body_size", "body_timeout", "decompress"], "arg_types": ["tornado.http1connection.HTTP1ConnectionParameters", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTP1ConnectionParameters", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "body_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ConnectionParameters.body_timeout", "name": "body_timeout", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}}}, "chunk_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ConnectionParameters.chunk_size", "name": "chunk_size", "type": "builtins.int"}}, "decompress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ConnectionParameters.decompress", "name": "decompress", "type": "builtins.bool"}}, "header_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ConnectionParameters.header_timeout", "name": "header_timeout", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}}}, "max_body_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ConnectionParameters.max_body_size", "name": "max_body_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "max_header_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ConnectionParameters.max_header_size", "name": "max_header_size", "type": "builtins.int"}}, "no_keep_alive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ConnectionParameters.no_keep_alive", "name": "no_keep_alive", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.http1connection.HTTP1ConnectionParameters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.http1connection.HTTP1ConnectionParameters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTP1ServerConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.http1connection.HTTP1ServerConnection", "name": "HTTP1ServerConnection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.http1connection.HTTP1ServerConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.http1connection", "mro": ["tornado.http1connection.HTTP1ServerConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "stream", "params", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1ServerConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "stream", "params", "context"], "arg_types": ["tornado.http1connection.HTTP1ServerConnection", "tornado.iostream.IOStream", {".class": "UnionType", "items": ["tornado.http1connection.HTTP1ConnectionParameters", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTP1ServerConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_server_request_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.http1connection.HTTP1ServerConnection._server_request_loop", "name": "_server_request_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "arg_types": ["tornado.http1connection.HTTP1ServerConnection", "tornado.httputil.HTTPServerConnectionDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_server_request_loop of HTTP1ServerConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_serving_future": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection.HTTP1ServerConnection._serving_future", "name": "_serving_future", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}, {".class": "NoneType"}]}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.http1connection.HTTP1ServerConnection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection.HTTP1ServerConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of HTTP1ServerConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ServerConnection.context", "name": "context", "type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ServerConnection.params", "name": "params", "type": "tornado.http1connection.HTTP1ConnectionParameters"}}, "start_serving": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.HTTP1ServerConnection.start_serving", "name": "start_serving", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delegate"], "arg_types": ["tornado.http1connection.HTTP1ServerConnection", "tornado.httputil.HTTPServerConnectionDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_serving of HTTP1ServerConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection.HTTP1ServerConnection.stream", "name": "stream", "type": "tornado.iostream.IOStream"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.http1connection.HTTP1ServerConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.http1connection.HTTP1ServerConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_ExceptionLoggingContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.http1connection._ExceptionLoggingContext", "name": "_ExceptionLoggingContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.http1connection._ExceptionLoggingContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.http1connection", "mro": ["tornado.http1connection._ExceptionLoggingContext", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection._ExceptionLoggingContext.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.http1connection._ExceptionLoggingContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of _ExceptionLoggingContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection._ExceptionLoggingContext.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["tornado.http1connection._ExceptionLoggingContext", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}, "types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of _ExceptionLoggingContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "logger"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection._ExceptionLoggingContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "logger"], "arg_types": ["tornado.http1connection._ExceptionLoggingContext", "logging.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ExceptionLoggingContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection._ExceptionLoggingContext.logger", "name": "logger", "type": "logging.Logger"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.http1connection._ExceptionLoggingContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.http1connection._ExceptionLoggingContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GzipMessageDelegate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httputil.HTTPMessageDelegate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.http1connection._GzipMessageDelegate", "name": "_GzipMessageDelegate", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.http1connection._GzipMessageDelegate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.http1connection", "mro": ["tornado.http1connection._GzipMessageDelegate", "tornado.httputil.HTTPMessageDelegate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delegate", "chunk_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection._GzipMessageDelegate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delegate", "chunk_size"], "arg_types": ["tornado.http1connection._GzipMessageDelegate", "tornado.httputil.HTTPMessageDelegate", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _GzipMessageDelegate", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_chunk_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection._GzipMessageDelegate._chunk_size", "name": "_chunk_size", "type": "builtins.int"}}, "_decompressor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.http1connection._GzipMessageDelegate._decompressor", "name": "_decompressor", "type": {".class": "UnionType", "items": ["tornado.util.GzipDecompressor", {".class": "NoneType"}]}}}, "_delegate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.http1connection._GzipMessageDelegate._delegate", "name": "_delegate", "type": "tornado.httputil.HTTPMessageDelegate"}}, "data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.http1connection._GzipMessageDelegate.data_received", "name": "data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": ["tornado.http1connection._GzipMessageDelegate", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_received of _GzipMessageDelegate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "finish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection._GzipMessageDelegate.finish", "name": "finish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection._GzipMessageDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finish of _GzipMessageDelegate", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "headers_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_line", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection._GzipMessageDelegate.headers_received", "name": "headers_received", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_line", "headers"], "arg_types": ["tornado.http1connection._GzipMessageDelegate", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.RequestStartLine"}, {".class": "TypeAliasType", "args": [], "type_ref": "tornado.httputil.ResponseStartLine"}]}, "tornado.httputil.HTTPHeaders"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers_received of _GzipMessageDelegate", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "typing.Awaitable"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "on_connection_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection._GzipMessageDelegate.on_connection_close", "name": "on_connection_close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection._GzipMessageDelegate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_close of _GzipMessageDelegate", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.http1connection._GzipMessageDelegate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.http1connection._GzipMessageDelegate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_QuietException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.http1connection._QuietException", "name": "_QuietException", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.http1connection._QuietException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.http1connection", "mro": ["tornado.http1connection._QuietException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection._QuietException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.http1connection._QuietException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _QuietException", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.http1connection._QuietException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.http1connection._QuietException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.http1connection.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.http1connection.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.http1connection.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.http1connection.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.http1connection.__package__", "name": "__package__", "type": "builtins.str"}}, "app_log": {".class": "SymbolTableNode", "cross_ref": "tornado.log.app_log", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "future_add_done_callback": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent.future_add_done_callback", "kind": "Gdef"}, "future_set_result_unless_cancelled": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent.future_set_result_unless_cancelled", "kind": "Gdef"}, "gen": {".class": "SymbolTableNode", "cross_ref": "tornado.gen", "kind": "Gdef"}, "gen_log": {".class": "SymbolTableNode", "cross_ref": "tornado.log.gen_log", "kind": "Gdef"}, "httputil": {".class": "SymbolTableNode", "cross_ref": "tornado.httputil", "kind": "Gdef"}, "iostream": {".class": "SymbolTableNode", "cross_ref": "tornado.iostream", "kind": "Gdef"}, "is_transfer_encoding_chunked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.is_transfer_encoding_chunked", "name": "is_transfer_encoding_chunked", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["headers"], "arg_types": ["tornado.httputil.HTTPHeaders"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_transfer_encoding_chunked", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "native_str": {".class": "SymbolTableNode", "cross_ref": "tornado.escape.native_str", "kind": "Gdef"}, "parse_hex_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.parse_hex_int", "name": "parse_hex_int", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_hex_int", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parse_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.http1connection.parse_int", "name": "parse_int", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_int", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "utf8": {".class": "SymbolTableNode", "cross_ref": "tornado.escape.utf8", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/http1connection.py"}