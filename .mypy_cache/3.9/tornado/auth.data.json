{".class": "MypyFile", "_fullname": "tornado.auth", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AuthError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.auth.AuthError", "name": "<PERSON>th<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.auth.AuthError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.auth", "mro": ["tornado.auth.AuthError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.auth.AuthError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.auth.AuthError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FacebookGraphMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.auth.OAuth2Mixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.auth.FacebookGraphMixin", "name": "FacebookGraphMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.auth.FacebookGraphMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.auth", "mro": ["tornado.auth.FacebookGraphMixin", "tornado.auth.OAuth2Mixin", "builtins.object"], "names": {".class": "SymbolTable", "_FACEBOOK_BASE_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.FacebookGraphMixin._FACEBOOK_BASE_URL", "name": "_FACEBOOK_BASE_URL", "type": "builtins.str"}}, "_OAUTH_ACCESS_TOKEN_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.FacebookGraphMixin._OAUTH_ACCESS_TOKEN_URL", "name": "_OAUTH_ACCESS_TOKEN_URL", "type": "builtins.str"}}, "_OAUTH_AUTHORIZE_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.FacebookGraphMixin._OAUTH_AUTHORIZE_URL", "name": "_OAUTH_AUTHORIZE_URL", "type": "builtins.str"}}, "_OAUTH_NO_CALLBACKS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.FacebookGraphMixin._OAUTH_NO_CALLBACKS", "name": "_OAUTH_NO_CALLBACKS", "type": "builtins.bool"}}, "facebook_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "path", "access_token", "post_args", "args"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.FacebookGraphMixin.facebook_request", "name": "facebook_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "path", "access_token", "post_args", "args"], "arg_types": ["tornado.auth.FacebookGraphMixin", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "facebook_request of FacebookGraphMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_authenticated_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "redirect_uri", "client_id", "client_secret", "code", "extra_fields"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.FacebookGraphMixin.get_authenticated_user", "name": "get_authenticated_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "redirect_uri", "client_id", "client_secret", "code", "extra_fields"], "arg_types": ["tornado.auth.FacebookGraphMixin", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_authenticated_user of FacebookGraphMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.auth.FacebookGraphMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.auth.FacebookGraphMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GoogleOAuth2Mixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.auth.OAuth2Mixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.auth.GoogleOAuth2Mixin", "name": "GoogleOAuth2Mixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.auth.GoogleOAuth2Mixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.auth", "mro": ["tornado.auth.GoogleOAuth2Mixin", "tornado.auth.OAuth2Mixin", "builtins.object"], "names": {".class": "SymbolTable", "_OAUTH_ACCESS_TOKEN_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.GoogleOAuth2Mixin._OAUTH_ACCESS_TOKEN_URL", "name": "_OAUTH_ACCESS_TOKEN_URL", "type": "builtins.str"}}, "_OAUTH_AUTHORIZE_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.GoogleOAuth2Mixin._OAUTH_AUTHORIZE_URL", "name": "_OAUTH_AUTHORIZE_URL", "type": "builtins.str"}}, "_OAUTH_NO_CALLBACKS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.GoogleOAuth2Mixin._OAUTH_NO_CALLBACKS", "name": "_OAUTH_NO_CALLBACKS", "type": "builtins.bool"}}, "_OAUTH_SETTINGS_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.GoogleOAuth2Mixin._OAUTH_SETTINGS_KEY", "name": "_OAUTH_SETTINGS_KEY", "type": "builtins.str"}}, "_OAUTH_USERINFO_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.GoogleOAuth2Mixin._OAUTH_USERINFO_URL", "name": "_OAUTH_USERINFO_URL", "type": "builtins.str"}}, "get_authenticated_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "redirect_uri", "code", "client_id", "client_secret"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.GoogleOAuth2Mixin.get_authenticated_user", "name": "get_authenticated_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "redirect_uri", "code", "client_id", "client_secret"], "arg_types": ["tornado.auth.GoogleOAuth2Mixin", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_authenticated_user of GoogleOAuth2Mixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_google_oauth_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.GoogleOAuth2Mixin.get_google_oauth_settings", "name": "get_google_oauth_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.auth.GoogleOAuth2Mixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_google_oauth_settings of GoogleOAuth2Mixin", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.auth.GoogleOAuth2Mixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.auth.GoogleOAuth2Mixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "OAuth2Mixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.auth.OAuth2Mixin", "name": "OAuth2Mixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.auth.OAuth2Mixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.auth", "mro": ["tornado.auth.OAuth2Mixin", "builtins.object"], "names": {".class": "SymbolTable", "_oauth_request_token_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "redirect_uri", "client_id", "client_secret", "code", "extra_params"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuth2Mixin._oauth_request_token_url", "name": "_oauth_request_token_url", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "redirect_uri", "client_id", "client_secret", "code", "extra_params"], "arg_types": ["tornado.auth.OAuth2Mixin", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_request_token_url of OAuth2Mixin", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "authorize_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "redirect_uri", "client_id", "client_secret", "extra_params", "scope", "response_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuth2Mixin.authorize_redirect", "name": "authorize_redirect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "redirect_uri", "client_id", "client_secret", "extra_params", "scope", "response_type"], "arg_types": ["tornado.auth.OAuth2Mixin", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authorize_redirect of OAuth2Mixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_auth_http_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuth2Mixin.get_auth_http_client", "name": "get_auth_http_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.auth.OAuth2Mixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_auth_http_client of OAuth2Mixin", "ret_type": "tornado.httpclient.AsyncHTTPClient", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "oauth2_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "url", "access_token", "post_args", "args"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.OAuth2Mixin.oauth2_request", "name": "oauth2_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "url", "access_token", "post_args", "args"], "arg_types": ["tornado.auth.OAuth2Mixin", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "oauth2_request of OAuth2Mixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.auth.OAuth2Mixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.auth.OAuth2Mixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OAuthMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.auth.OAuthMixin", "name": "OAuthMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.auth.OAuthMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.auth", "mro": ["tornado.auth.OAuthMixin", "builtins.object"], "names": {".class": "SymbolTable", "_oauth_access_token_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request_token"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuthMixin._oauth_access_token_url", "name": "_oauth_access_token_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request_token"], "arg_types": ["tornado.auth.OAuthMixin", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_access_token_url of OAuthMixin", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_oauth_consumer_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuthMixin._oauth_consumer_token", "name": "_oauth_consumer_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.auth.OAuthMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_consumer_token of OAuthMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_oauth_get_user_future": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "access_token"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.OAuthMixin._oauth_get_user_future", "name": "_oauth_get_user_future", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "access_token"], "arg_types": ["tornado.auth.OAuthMixin", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_get_user_future of OAuthMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_oauth_request_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "url", "access_token", "parameters", "method"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuthMixin._oauth_request_parameters", "name": "_oauth_request_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "url", "access_token", "parameters", "method"], "arg_types": ["tornado.auth.OAuthMixin", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_request_parameters of OAuthMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_oauth_request_token_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "callback_uri", "extra_params"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuthMixin._oauth_request_token_url", "name": "_oauth_request_token_url", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "callback_uri", "extra_params"], "arg_types": ["tornado.auth.OAuthMixin", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_request_token_url of OAuthMixin", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_on_request_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "authorize_url", "callback_uri", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuthMixin._on_request_token", "name": "_on_request_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "authorize_url", "callback_uri", "response"], "arg_types": ["tornado.auth.OAuthMixin", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_request_token of OAuthMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "authorize_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "callback_uri", "extra_params", "http_client"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.OAuthMixin.authorize_redirect", "name": "authorize_redirect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "callback_uri", "extra_params", "http_client"], "arg_types": ["tornado.auth.OAuthMixin", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["tornado.httpclient.AsyncHTTPClient", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authorize_redirect of OAuthMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_auth_http_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OAuthMixin.get_auth_http_client", "name": "get_auth_http_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.auth.OAuthMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_auth_http_client of OAuthMixin", "ret_type": "tornado.httpclient.AsyncHTTPClient", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_authenticated_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "http_client"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.OAuthMixin.get_authenticated_user", "name": "get_authenticated_user", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "http_client"], "arg_types": ["tornado.auth.OAuthMixin", {".class": "UnionType", "items": ["tornado.httpclient.AsyncHTTPClient", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_authenticated_user of OAuthMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.auth.OAuthMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.auth.OAuthMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OpenIdMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.auth.OpenIdMixin", "name": "OpenIdMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.auth.OpenIdMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.auth", "mro": ["tornado.auth.OpenIdMixin", "builtins.object"], "names": {".class": "SymbolTable", "_on_authentication_verified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OpenIdMixin._on_authentication_verified", "name": "_on_authentication_verified", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["tornado.auth.OpenIdMixin", "tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_authentication_verified of OpenIdMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_openid_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "callback_uri", "ax_attrs", "oauth_scope"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OpenIdMixin._openid_args", "name": "_openid_args", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "callback_uri", "ax_attrs", "oauth_scope"], "arg_types": ["tornado.auth.OpenIdMixin", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_openid_args of OpenIdMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "authenticate_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "callback_uri", "ax_attrs"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OpenIdMixin.authenticate_redirect", "name": "authenticate_redirect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "callback_uri", "ax_attrs"], "arg_types": ["tornado.auth.OpenIdMixin", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authenticate_redirect of OpenIdMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_auth_http_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.OpenIdMixin.get_auth_http_client", "name": "get_auth_http_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.auth.OpenIdMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_auth_http_client of OpenIdMixin", "ret_type": "tornado.httpclient.AsyncHTTPClient", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_authenticated_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "http_client"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.OpenIdMixin.get_authenticated_user", "name": "get_authenticated_user", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "http_client"], "arg_types": ["tornado.auth.OpenIdMixin", {".class": "UnionType", "items": ["tornado.httpclient.AsyncHTTPClient", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_authenticated_user of OpenIdMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.auth.OpenIdMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.auth.OpenIdMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RequestHandler": {".class": "SymbolTableNode", "cross_ref": "tornado.web.RequestHandler", "kind": "Gdef"}, "TwitterMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.auth.OAuthMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.auth.TwitterMixin", "name": "TwitterMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.auth.TwitterMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.auth", "mro": ["tornado.auth.TwitterMixin", "tornado.auth.OAuthMixin", "builtins.object"], "names": {".class": "SymbolTable", "_OAUTH_ACCESS_TOKEN_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.TwitterMixin._OAUTH_ACCESS_TOKEN_URL", "name": "_OAUTH_ACCESS_TOKEN_URL", "type": "builtins.str"}}, "_OAUTH_AUTHENTICATE_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.TwitterMixin._OAUTH_AUTHENTICATE_URL", "name": "_OAUTH_AUTHENTICATE_URL", "type": "builtins.str"}}, "_OAUTH_AUTHORIZE_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.TwitterMixin._OAUTH_AUTHORIZE_URL", "name": "_OAUTH_AUTHORIZE_URL", "type": "builtins.str"}}, "_OAUTH_NO_CALLBACKS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.TwitterMixin._OAUTH_NO_CALLBACKS", "name": "_OAUTH_NO_CALLBACKS", "type": "builtins.bool"}}, "_OAUTH_REQUEST_TOKEN_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.TwitterMixin._OAUTH_REQUEST_TOKEN_URL", "name": "_OAUTH_REQUEST_TOKEN_URL", "type": "builtins.str"}}, "_TWITTER_BASE_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.auth.TwitterMixin._TWITTER_BASE_URL", "name": "_TWITTER_BASE_URL", "type": "builtins.str"}}, "_oauth_consumer_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth.TwitterMixin._oauth_consumer_token", "name": "_oauth_consumer_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.auth.TwitterMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_consumer_token of TwitterMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_oauth_get_user_future": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "access_token"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.TwitterMixin._oauth_get_user_future", "name": "_oauth_get_user_future", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "access_token"], "arg_types": ["tornado.auth.TwitterMixin", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_get_user_future of TwitterMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "authenticate_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "callback_uri"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.TwitterMixin.authenticate_redirect", "name": "authenticate_redirect", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "callback_uri"], "arg_types": ["tornado.auth.TwitterMixin", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authenticate_redirect of TwitterMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "twitter_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "path", "access_token", "post_args", "args"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.auth.TwitterMixin.twitter_request", "name": "twitter_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "path", "access_token", "post_args", "args"], "arg_types": ["tornado.auth.TwitterMixin", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "twitter_request of TwitterMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.auth.TwitterMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.auth.TwitterMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.auth.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.auth.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.auth.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.auth.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.auth.__package__", "name": "__package__", "type": "builtins.str"}}, "_oauth10a_signature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["consumer_token", "method", "url", "parameters", "token"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth._oauth10a_signature", "name": "_oauth10a_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["consumer_token", "method", "url", "parameters", "token"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth10a_signature", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_oauth_escape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth._oauth_escape", "name": "_oauth_escape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_escape", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_oauth_parse_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["body"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth._oauth_parse_response", "name": "_oauth_parse_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["body"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_parse_response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_oauth_signature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["consumer_token", "method", "url", "parameters", "token"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.auth._oauth_signature", "name": "_oauth_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["consumer_token", "method", "url", "parameters", "token"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_oauth_signature", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "binascii": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "escape": {".class": "SymbolTableNode", "cross_ref": "tornado.escape", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "hmac": {".class": "SymbolTableNode", "cross_ref": "hmac", "kind": "Gdef"}, "httpclient": {".class": "SymbolTableNode", "cross_ref": "tornado.httpclient", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "unicode_type": {".class": "SymbolTableNode", "cross_ref": "tornado.util.unicode_type", "kind": "Gdef"}, "url_concat": {".class": "SymbolTableNode", "cross_ref": "tornado.httputil.url_concat", "kind": "Gdef"}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/auth.py"}