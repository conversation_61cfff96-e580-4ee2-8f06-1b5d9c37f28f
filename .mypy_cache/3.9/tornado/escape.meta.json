{"data_mtime": 1753508936, "dep_lines": [31, 33, 28, 29, 30, 31, 35, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 10, 20, 5, 5, 30, 30], "dependencies": ["urllib.parse", "tornado.util", "html", "json", "re", "urllib", "typing", "builtins", "abc", "typing_extensions"], "hash": "d02b7247b091207e62551ab9792ce1ab90bf1b815036937b231a87221b7211bd", "id": "tornado.escape", "ignore_all": true, "interface_hash": "998ca9d70faf1e713a7022629137b451cdfe004b50664a4676dd4d68162bbd7d", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/escape.py", "plugin_data": null, "size": 14221, "suppressed": [], "version_id": "1.8.0"}