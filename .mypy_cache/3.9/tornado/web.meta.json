{"data_mtime": 1753508937, "dep_lines": [62, 67, 72, 82, 85, 86, 87, 88, 89, 90, 91, 92, 93, 95, 105, 2232, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 109, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 10, 5, 10, 10, 10, 5, 10, 5, 5, 20, 10, 10, 10, 20, 10, 10, 10, 10, 20, 5, 5, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["email.utils", "http.cookies", "os.path", "urllib.parse", "tornado.concurrent", "tornado.escape", "tornado.gen", "tornado.httpserver", "tornado.httputil", "tornado.iostream", "tornado.locale", "tornado.log", "tornado.template", "tornado.routing", "tornado.util", "tornado.autoreload", "base64", "<PERSON><PERSON><PERSON><PERSON>", "datetime", "email", "functools", "gzip", "<PERSON><PERSON><PERSON>", "hmac", "http", "inspect", "io", "mimetypes", "numbers", "os", "re", "socket", "sys", "threading", "time", "warnings", "tornado", "traceback", "types", "urllib", "typing", "builtins", "_collections_abc", "_compression", "_typeshed", "_warnings", "abc", "asyncio", "asyncio.events", "asyncio.futures", "concurrent", "concurrent.futures", "concurrent.futures._base", "<PERSON><PERSON><PERSON>", "enum", "logging", "tornado.netutil", "tornado.tcpserver", "typing_extensions"], "hash": "74b855253b0c68bedd7fb6cfe397e4e22219f66a02dbb42852fdb2c5b276e49b", "id": "tornado.web", "ignore_all": true, "interface_hash": "6379f2d85ec3ff520c49159cf4de268e9f865cfc39bbee73446acc36ce8ea214", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/web.py", "plugin_data": null, "size": 145698, "suppressed": [], "version_id": "1.8.0"}