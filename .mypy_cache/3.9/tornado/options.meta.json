{"data_mtime": 1753508936, "dep_lines": [109, 110, 111, 102, 103, 104, 105, 106, 107, 113, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30], "dependencies": ["tornado.escape", "tornado.log", "tornado.util", "datetime", "numbers", "re", "sys", "os", "textwrap", "typing", "builtins", "abc", "enum", "typing_extensions"], "hash": "efcc6111329a331c2b26899495338e8e995d8f41244c22483ec10d42c31f8bce", "id": "tornado.options", "ignore_all": true, "interface_hash": "e150caab22b3317f812ed430b65e28b8e1e70934f856f3cf6a1e77d60f27f1f9", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/options.py", "plugin_data": null, "size": 25860, "suppressed": [], "version_id": "1.8.0"}