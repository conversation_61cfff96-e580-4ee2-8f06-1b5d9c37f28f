{"data_mtime": 1753508937, "dep_lines": [31, 32, 33, 34, 35, 36, 37, 28, 29, 33, 39, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 10, 10, 20, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["tornado.escape", "tornado.http1connection", "tornado.httputil", "tornado.iostream", "tornado.netutil", "tornado.tcpserver", "tornado.util", "socket", "ssl", "tornado", "typing", "builtins", "_socket", "_typeshed", "abc", "enum", "typing_extensions"], "hash": "223c05ed18b0d6788cf9fcea82612c1314cfe5fae4ac6787e0c609c11ddf7f82", "id": "tornado.httpserver", "ignore_all": true, "interface_hash": "d038e727d838c517e20b2ff4b11063f860e7af7736884a4db22e7a982158a23d", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/httpserver.py", "plugin_data": null, "size": 16131, "suppressed": [], "version_id": "1.8.0"}