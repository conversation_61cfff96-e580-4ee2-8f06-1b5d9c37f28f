{"data_mtime": 1753508937, "dep_lines": [19, 27, 28, 29, 18, 19, 20, 21, 22, 23, 24, 25, 31, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 20, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures", "tornado.concurrent", "tornado.ioloop", "tornado.util", "asyncio", "concurrent", "errno", "os", "sys", "socket", "ssl", "stat", "typing", "builtins", "_socket", "abc", "concurrent.futures._base", "concurrent.futures.thread", "enum", "types", "typing_extensions"], "hash": "454b6dc55c4c02b6b4457747a015821ac523773d3a087a7e6a11f5b754d3c88e", "id": "tornado.netutil", "ignore_all": true, "interface_hash": "a43e7b2c27f675f2ab2f19f04782a7aa81adf32bb6002a879620dd0d52355c32", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/netutil.py", "plugin_data": null, "size": 25077, "suppressed": [], "version_id": "1.8.0"}