{"data_mtime": 1753508937, "dep_lines": [30, 13, 25, 26, 27, 28, 29, 31, 32, 33, 34, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 36, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tornado.platform.asyncio", "collections.abc", "tornado.gen", "tornado.httpclient", "tornado.httpserver", "tornado.ioloop", "tornado.netutil", "tornado.process", "tornado.log", "tornado.util", "tornado.web", "asyncio", "functools", "inspect", "logging", "os", "re", "signal", "socket", "sys", "unittest", "warnings", "tornado", "typing", "types", "builtins", "_socket", "_typeshed", "_warnings", "abc", "asyncio.events", "datetime", "tornado.httputil", "tornado.platform", "tornado.routing", "tornado.tcpserver", "typing_extensions", "unittest.case", "unittest.result"], "hash": "bf6bc9cc6254d49499b430aaad937d9ac84fc68ad20cf40b75cb55470e56fb74", "id": "tornado.testing", "ignore_all": true, "interface_hash": "4decc0aa98b1dfb674b976ef99503d6e55f7ecdb9c261ae1395b50898450d8b9", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/testing.py", "plugin_data": null, "size": 33153, "suppressed": [], "version_id": "1.8.0"}