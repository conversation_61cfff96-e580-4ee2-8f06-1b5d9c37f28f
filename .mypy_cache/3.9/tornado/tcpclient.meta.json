{"data_mtime": 1753508937, "dep_lines": [26, 27, 28, 29, 30, 19, 20, 21, 22, 23, 24, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tornado.concurrent", "tornado.ioloop", "tornado.iostream", "tornado.gen", "tornado.netutil", "functools", "socket", "numbers", "datetime", "ssl", "typing", "tornado", "builtins", "_typeshed", "abc", "asyncio", "asyncio.events", "asyncio.futures", "enum", "tornado.util", "types"], "hash": "043b36fb74bd8f02c9ca4f6dafa8a3a9d4e603db5ecdbbf6ab2ad8a1b3ed76e8", "id": "tornado.tcpclient", "ignore_all": true, "interface_hash": "234beaad61d04a4f2420cc5f746552a6ab46c43bfa1e2d6d88f9a482ac1978a3", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/tcpclient.py", "plugin_data": null, "size": 12126, "suppressed": [], "version_id": "1.8.0"}