{".class": "MypyFile", "_fullname": "tornado.netutil", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "BlockingResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.netutil.ExecutorResolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.netutil.BlockingResolver", "name": "BlockingResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.netutil.BlockingResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.netutil", "mro": ["tornado.netutil.BlockingResolver", "tornado.netutil.ExecutorResolver", "tornado.netutil.Resolver", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.BlockingResolver.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.netutil.BlockingResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of BlockingResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.netutil.BlockingResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.netutil.BlockingResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Configurable": {".class": "SymbolTableNode", "cross_ref": "tornado.util.Configurable", "kind": "Gdef"}, "DefaultExecutorResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.netutil.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.netutil.DefaultExecutorResolver", "name": "DefaultExecutorResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.netutil.DefaultExecutorResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.netutil", "mro": ["tornado.netutil.DefaultExecutorResolver", "tornado.netutil.Resolver", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.netutil.DefaultExecutorResolver.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "arg_types": ["tornado.netutil.DefaultExecutorResolver", "builtins.str", "builtins.int", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of DefaultExecutorResolver", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.netutil.DefaultExecutorResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.netutil.DefaultExecutorResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultLoopResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.netutil.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.netutil.DefaultLoopResolver", "name": "DefaultLoopResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.netutil.DefaultLoopResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.netutil", "mro": ["tornado.netutil.DefaultLoopResolver", "tornado.netutil.Resolver", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "tornado.netutil.DefaultLoopResolver.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "arg_types": ["tornado.netutil.DefaultLoopResolver", "builtins.str", "builtins.int", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of DefaultLoopResolver", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.netutil.DefaultLoopResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.netutil.DefaultLoopResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExecutorResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.netutil.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.netutil.ExecutorResolver", "name": "ExecutorResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.netutil.ExecutorResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.netutil", "mro": ["tornado.netutil.ExecutorResolver", "tornado.netutil.Resolver", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.ExecutorResolver.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.netutil.ExecutorResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of ExecutorResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close_executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.netutil.ExecutorResolver.close_executor", "name": "close_executor", "type": "builtins.bool"}}, "executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.netutil.ExecutorResolver.executor", "name": "executor", "type": "concurrent.futures._base.Executor"}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "executor", "close_executor"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.ExecutorResolver.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "executor", "close_executor"], "arg_types": ["tornado.netutil.ExecutorResolver", {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of ExecutorResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tornado.netutil.ExecutorResolver.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "arg_types": ["tornado.netutil.ExecutorResolver", "builtins.str", "builtins.int", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of ExecutorResolver", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tornado.netutil.ExecutorResolver.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "resolve of ExecutorResolver", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.netutil.ExecutorResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.netutil.ExecutorResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IOLoop": {".class": "SymbolTableNode", "cross_ref": "tornado.ioloop.IOLoop", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OverrideResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.netutil.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.netutil.OverrideResolver", "name": "OverrideResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.netutil.OverrideResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.netutil", "mro": ["tornado.netutil.OverrideResolver", "tornado.netutil.Resolver", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.OverrideResolver.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.netutil.OverrideResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of OverrideResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver", "mapping"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.OverrideResolver.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver", "mapping"], "arg_types": ["tornado.netutil.OverrideResolver", "tornado.netutil.Resolver", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of OverrideResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.netutil.OverrideResolver.mapping", "name": "mapping", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "builtins.dict"}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.OverrideResolver.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "arg_types": ["tornado.netutil.OverrideResolver", "builtins.str", "builtins.int", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of OverrideResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "resolver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.netutil.OverrideResolver.resolver", "name": "resolver", "type": "tornado.netutil.Resolver"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.netutil.OverrideResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.netutil.OverrideResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Resolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.util.Configurable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.netutil.Resolver", "name": "Resolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.netutil.Resolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.netutil", "mro": ["tornado.netutil.Resolver", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.Resolver.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.netutil.Resolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of Resolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "configurable_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.netutil.Resolver.configurable_base", "name": "configurable_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.netutil.Resolver"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configurable_base of Resolver", "ret_type": {".class": "TypeType", "item": "tornado.netutil.Resolver"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.netutil.Resolver.configurable_base", "name": "configurable_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.netutil.Resolver"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configurable_base of Resolver", "ret_type": {".class": "TypeType", "item": "tornado.netutil.Resolver"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "configurable_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.netutil.Resolver.configurable_default", "name": "configurable_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.netutil.Resolver"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configurable_default of Resolver", "ret_type": {".class": "TypeType", "item": "tornado.netutil.Resolver"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.netutil.Resolver.configurable_default", "name": "configurable_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.netutil.Resolver"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configurable_default of Resolver", "ret_type": {".class": "TypeType", "item": "tornado.netutil.Resolver"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.Resolver.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "family"], "arg_types": ["tornado.netutil.Resolver", "builtins.str", "builtins.int", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of Resolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.netutil.Resolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.netutil.Resolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThreadedResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.netutil.ExecutorResolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.netutil.ThreadedResolver", "name": "ThreadedResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.netutil.ThreadedResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.netutil", "mro": ["tornado.netutil.ThreadedResolver", "tornado.netutil.ExecutorResolver", "tornado.netutil.Resolver", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "_create_threadpool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "num_threads"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.netutil.ThreadedResolver._create_threadpool", "name": "_create_threadpool", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "num_threads"], "arg_types": [{".class": "TypeType", "item": "tornado.netutil.ThreadedResolver"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_threadpool of ThreadedResolver", "ret_type": "concurrent.futures.thread.ThreadPoolExecutor", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.netutil.ThreadedResolver._create_threadpool", "name": "_create_threadpool", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "num_threads"], "arg_types": [{".class": "TypeType", "item": "tornado.netutil.ThreadedResolver"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_threadpool of ThreadedResolver", "ret_type": "concurrent.futures.thread.ThreadPoolExecutor", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_threadpool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "tornado.netutil.ThreadedResolver._threadpool", "name": "_threadpool", "type": {".class": "UnionType", "items": ["concurrent.futures.thread.ThreadPoolExecutor", {".class": "NoneType"}]}}}, "_threadpool_pid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "tornado.netutil.ThreadedResolver._threadpool_pid", "name": "_threadpool_pid", "type": "builtins.int"}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "num_threads"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.ThreadedResolver.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "num_threads"], "arg_types": ["tornado.netutil.ThreadedResolver", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of ThreadedResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.netutil.ThreadedResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.netutil.ThreadedResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_DEFAULT_BACKLOG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.netutil._DEFAULT_BACKLOG", "name": "_DEFAULT_BACKLOG", "type": "builtins.int"}}, "_SSL_CONTEXT_KEYWORDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.netutil._SSL_CONTEXT_KEYWORDS", "name": "_SSL_CONTEXT_KEYWORDS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.frozenset"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.netutil.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.netutil.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.netutil.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.netutil.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.netutil.__package__", "name": "__package__", "type": "builtins.str"}}, "_client_ssl_defaults": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.netutil._client_ssl_defaults", "name": "_client_ssl_defaults", "type": "ssl.SSLContext"}}, "_resolve_addr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["host", "port", "family"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil._resolve_addr", "name": "_resolve_addr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["host", "port", "family"], "arg_types": ["builtins.str", "builtins.int", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_addr", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_server_ssl_defaults": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tornado.netutil._server_ssl_defaults", "name": "_server_ssl_defaults", "type": "ssl.SSLContext"}}, "add_accept_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["sock", "callback"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.add_accept_handler", "name": "add_accept_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sock", "callback"], "arg_types": ["socket.socket", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["socket.socket", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_accept_handler", "ret_type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "bind_sockets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["port", "address", "family", "backlog", "flags", "reuse_port"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.bind_sockets", "name": "bind_sockets", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["port", "address", "family", "backlog", "flags", "reuse_port"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "socket.AddressFamily", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_sockets", "ret_type": {".class": "Instance", "args": ["socket.socket"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bind_unix_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["file", "mode", "backlog"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.bind_unix_socket", "name": "bind_unix_socket", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["file", "mode", "backlog"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_unix_socket", "ret_type": "socket.socket", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "concurrent": {".class": "SymbolTableNode", "cross_ref": "concurrent", "kind": "Gdef"}, "dummy_executor": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent.dummy_executor", "kind": "Gdef"}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef"}, "errno_from_exception": {".class": "SymbolTableNode", "cross_ref": "tornado.util.errno_from_exception", "kind": "Gdef"}, "is_valid_ip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ip"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.is_valid_ip", "name": "is_valid_ip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ip"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_ip", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "run_on_executor": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent.run_on_executor", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "ssl_options_to_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["ssl_options", "server_side"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.ssl_options_to_context", "name": "ssl_options_to_context", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["ssl_options", "server_side"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "ssl.SSLContext"]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ssl_options_to_context", "ret_type": "ssl.SSLContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ssl_wrap_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["socket", "ssl_options", "server_hostname", "server_side", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.netutil.ssl_wrap_socket", "name": "ssl_wrap_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["socket", "ssl_options", "server_hostname", "server_side", "kwargs"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "ssl.SSLContext"]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ssl_wrap_socket", "ret_type": "ssl.SSLSocket", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "stat": {".class": "SymbolTableNode", "cross_ref": "stat", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/netutil.py"}