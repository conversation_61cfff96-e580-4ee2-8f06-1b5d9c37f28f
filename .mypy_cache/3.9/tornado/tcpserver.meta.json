{"data_mtime": 1753508937, "dep_lines": [23, 24, 25, 26, 27, 33, 34, 18, 19, 20, 21, 23, 36, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 10, 10, 10, 10, 20, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["tornado.gen", "tornado.log", "tornado.ioloop", "tornado.iostream", "tornado.netutil", "tornado.process", "tornado.util", "errno", "os", "socket", "ssl", "tornado", "typing", "builtins", "_collections_abc", "_socket", "_typeshed", "abc", "enum", "genericpath"], "hash": "3fd16787209c7b3a91c67474d0ca6c28dd643d64f93e61930072501b62c12089", "id": "tornado.tcpserver", "ignore_all": true, "interface_hash": "c197534013e96ebed25499ca8b352ff6f79ee9ef7f54b82fa4d16b3a5cb83fed", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/tcpserver.py", "plugin_data": null, "size": 15006, "suppressed": [], "version_id": "1.8.0"}