{"data_mtime": 1753508937, "dep_lines": [202, 207, 208, 209, 199, 200, 201, 202, 203, 204, 205, 207, 211, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 5, 10, 20, 10, 10, 10, 20, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["os.path", "tornado.escape", "tornado.log", "tornado.util", "datetime", "io", "linecache", "os", "posixpath", "re", "threading", "tornado", "typing", "builtins", "_ast", "abc", "contextlib", "logging", "types", "typing_extensions"], "hash": "db920c86bc7d7629045a8592b3ee538d6c3ae39a82c41e8ea1a6ebdb7acb9c3d", "id": "tornado.template", "ignore_all": true, "interface_hash": "8f8149e25e9a58ab70c93f292ae1993ad39a9de30677f532c62e9be08fc21a73", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/template.py", "plugin_data": null, "size": 37670, "suppressed": [], "version_id": "1.8.0"}