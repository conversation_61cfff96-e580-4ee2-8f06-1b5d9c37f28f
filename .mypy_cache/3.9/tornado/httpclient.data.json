{".class": "MypyFile", "_fullname": "tornado.httpclient", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"HTTPRequest\" and \"_RequestProxy\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.HTTPRequest", "tornado.httpclient._RequestProxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "name": "<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "tornado.httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.httpclient", "mro": ["tornado.httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "tornado.httpclient.HTTPRequest", "tornado.httpclient._RequestProxy", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"HTTPRequest\" and \"_RequestProxy\">1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.httpclient.HTTPRequest", "tornado.httpclient._RequestProxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">1", "name": "<subclass of \"HTTPRequest\" and \"_RequestProxy\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "tornado.httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.httpclient", "mro": ["tornado.httpclient.<subclass of \"HTTPRequest\" and \"_RequestProxy\">1", "tornado.httpclient.HTTPRequest", "tornado.httpclient._RequestProxy", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncHTTPClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tornado.util.Configurable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.httpclient.AsyncHTTPClient", "name": "AsyncHTTPClient", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.httpclient.AsyncHTTPClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.httpclient", "mro": ["tornado.httpclient.AsyncHTTPClient", "tornado.util.Configurable", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["cls", "force_instance", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "tornado.httpclient.AsyncHTTPClient.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["cls", "force_instance", "kwargs"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of AsyncHTTPClient", "ret_type": "tornado.httpclient.AsyncHTTPClient", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_async_clients": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.httpclient.AsyncHTTPClient._async_clients", "name": "_async_clients", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_async_clients of AsyncHTTPClient", "ret_type": {".class": "Instance", "args": ["tornado.ioloop.IOLoop", "tornado.httpclient.AsyncHTTPClient"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.AsyncHTTPClient._async_clients", "name": "_async_clients", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_async_clients of AsyncHTTPClient", "ret_type": {".class": "Instance", "args": ["tornado.ioloop.IOLoop", "tornado.httpclient.AsyncHTTPClient"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_closed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.AsyncHTTPClient._closed", "name": "_closed", "type": "builtins.bool"}}, "_instance_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "tornado.httpclient.AsyncHTTPClient._instance_cache", "name": "_instance_cache", "type": {".class": "Instance", "args": ["tornado.ioloop.IOLoop", "tornado.httpclient.AsyncHTTPClient"], "type_ref": "builtins.dict"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.AsyncHTTPClient.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.AsyncHTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of AsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "configurable_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.httpclient.AsyncHTTPClient.configurable_base", "name": "configurable_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configurable_base of AsyncHTTPClient", "ret_type": {".class": "TypeType", "item": "tornado.util.Configurable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.AsyncHTTPClient.configurable_base", "name": "configurable_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configurable_base of AsyncHTTPClient", "ret_type": {".class": "TypeType", "item": "tornado.util.Configurable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "configurable_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.httpclient.AsyncHTTPClient.configurable_default", "name": "configurable_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configurable_default of AsyncHTTPClient", "ret_type": {".class": "TypeType", "item": "tornado.util.Configurable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.AsyncHTTPClient.configurable_default", "name": "configurable_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configurable_default of AsyncHTTPClient", "ret_type": {".class": "TypeType", "item": "tornado.util.Configurable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "configure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "impl", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "tornado.httpclient.AsyncHTTPClient.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "impl", "kwargs"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "TypeType", "item": "tornado.util.Configurable"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure of AsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.AsyncHTTPClient.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "impl", "kwargs"], "arg_types": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "TypeType", "item": "tornado.util.Configurable"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure of AsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "defaults": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.AsyncHTTPClient.defaults", "name": "defaults", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}}}, "fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "request", "raise_error", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.AsyncHTTPClient.fetch", "name": "fetch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "request", "raise_error", "kwargs"], "arg_types": ["tornado.httpclient.AsyncHTTPClient", {".class": "UnionType", "items": ["builtins.str", "tornado.httpclient.HTTPRequest"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch of AsyncHTTPClient", "ret_type": {".class": "Instance", "args": ["tornado.httpclient.HTTPResponse"], "type_ref": "asyncio.futures.Future"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fetch_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "callback"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.AsyncHTTPClient.fetch_impl", "name": "fetch_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "callback"], "arg_types": ["tornado.httpclient.AsyncHTTPClient", "tornado.httpclient.HTTPRequest", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_impl of AsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "defaults"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.AsyncHTTPClient.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "defaults"], "arg_types": ["tornado.httpclient.AsyncHTTPClient", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of AsyncHTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "io_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.AsyncHTTPClient.io_loop", "name": "io_loop", "type": "tornado.ioloop.IOLoop"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.httpclient.AsyncHTTPClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.httpclient.AsyncHTTPClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "io.BytesIO", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Configurable": {".class": "SymbolTableNode", "cross_ref": "tornado.util.Configurable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Future": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent.Future", "kind": "Gdef"}, "HTTPClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.httpclient.HTTPClient", "name": "HTTPClient", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.httpclient.HTTPClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.httpclient", "mro": ["tornado.httpclient.HTTPClient", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPClient.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of HTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "async_client_class", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "async_client_class", "kwargs"], "arg_types": ["tornado.httpclient.HTTPClient", {".class": "UnionType", "items": [{".class": "TypeType", "item": "tornado.httpclient.AsyncHTTPClient"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_async_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPClient._async_client", "name": "_async_client", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_closed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPClient._closed", "name": "_closed", "type": "builtins.bool"}}, "_io_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPClient._io_loop", "name": "_io_loop", "type": "tornado.ioloop.IOLoop"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPClient.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of HTTPClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPClient.fetch", "name": "fetch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "arg_types": ["tornado.httpclient.HTTPClient", {".class": "UnionType", "items": ["tornado.httpclient.HTTPRequest", "builtins.str"]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch of HTTPClient", "ret_type": "tornado.httpclient.HTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.httpclient.HTTPClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.httpclient.HTTPClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPClientError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.httpclient.HTTPClientError", "name": "HTTPClientError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.httpclient.HTTPClientError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.httpclient", "mro": ["tornado.httpclient.HTTPClientError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "code", "message", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPClientError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "code", "message", "response"], "arg_types": ["tornado.httpclient.HTTPClientError", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["tornado.httpclient.HTTPResponse", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPClientError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "tornado.httpclient.HTTPClientError.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPClientError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPClientError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPClientError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of HTTPClientError", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPClientError.code", "name": "code", "type": "builtins.int"}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPClientError.message", "name": "message", "type": "builtins.str"}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPClientError.response", "name": "response", "type": {".class": "UnionType", "items": ["tornado.httpclient.HTTPResponse", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.httpclient.HTTPClientError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.httpclient.HTTPClientError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "tornado.httpclient.HTTPError", "line": 732, "no_args": true, "normalized": false, "target": "tornado.httpclient.HTTPClientError"}}, "HTTPRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.httpclient.HTTPRequest", "name": "HTTPRequest", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.httpclient.HTTPRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.httpclient", "mro": ["tornado.httpclient.HTTPRequest", "builtins.object"], "names": {".class": "SymbolTable", "_DEFAULTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "tornado.httpclient.HTTPRequest._DEFAULTS", "name": "_DEFAULTS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "url", "method", "headers", "body", "auth_username", "auth_password", "auth_mode", "connect_timeout", "request_timeout", "if_modified_since", "follow_redirects", "max_redirects", "user_agent", "use_gzip", "network_interface", "streaming_callback", "header_callback", "prepare_curl_callback", "proxy_host", "proxy_port", "proxy_username", "proxy_password", "proxy_auth_mode", "allow_nonstandard_methods", "validate_cert", "ca_certs", "allow_ipv6", "client_key", "client_cert", "body_producer", "expect_100_continue", "decompress_response", "ssl_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "url", "method", "headers", "body", "auth_username", "auth_password", "auth_mode", "connect_timeout", "request_timeout", "if_modified_since", "follow_redirects", "max_redirects", "user_agent", "use_gzip", "network_interface", "streaming_callback", "header_callback", "prepare_curl_callback", "proxy_host", "proxy_port", "proxy_username", "proxy_password", "proxy_auth_mode", "allow_nonstandard_methods", "validate_cert", "ca_certs", "allow_ipv6", "client_key", "client_cert", "body_producer", "expect_100_continue", "decompress_response", "ssl_options"], "arg_types": ["tornado.httpclient.HTTPRequest", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, "tornado.httputil.HTTPHeaders", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", "datetime.datetime", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "ssl.SSLContext", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest._body", "name": "_body", "type": "builtins.bytes"}}, "_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "tornado.httpclient.HTTPRequest._headers", "name": "_headers", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, "tornado.httputil.HTTPHeaders"]}}}, "allow_ipv6": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.allow_ipv6", "name": "allow_ipv6", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "allow_nonstandard_methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.allow_nonstandard_methods", "name": "allow_nonstandard_methods", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "auth_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.auth_mode", "name": "auth_mode", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "auth_password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.auth_password", "name": "auth_password", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "auth_username": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.auth_username", "name": "auth_username", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "tornado.httpclient.HTTPRequest.body", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "tornado.httpclient.HTTPRequest.body", "name": "body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "body of HTTPRequest", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.body", "name": "body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "body of HTTPRequest", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tornado.httpclient.HTTPRequest.body", "name": "body", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["tornado.httpclient.HTTPRequest", {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "body of HTTPRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "body", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "body of HTTPRequest", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "body_producer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.body_producer", "name": "body_producer", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "asyncio.futures.Future"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "ca_certs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.ca_certs", "name": "ca_certs", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "client_cert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.client_cert", "name": "client_cert", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "client_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.client_key", "name": "client_key", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "connect_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.connect_timeout", "name": "connect_timeout", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}}}, "decompress_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.decompress_response", "name": "decompress_response", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "expect_100_continue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.expect_100_continue", "name": "expect_100_continue", "type": "builtins.bool"}}, "follow_redirects": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.follow_redirects", "name": "follow_redirects", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "header_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.header_callback", "name": "header_callback", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "tornado.httpclient.HTTPRequest.headers", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "tornado.httpclient.HTTPRequest.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of HTTPRequest", "ret_type": "tornado.httputil.HTTPHeaders", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of HTTPRequest", "ret_type": "tornado.httputil.HTTPHeaders", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "tornado.httpclient.HTTPRequest.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["tornado.httpclient.HTTPRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, "tornado.httputil.HTTPHeaders"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of HTTPRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "headers", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of HTTPRequest", "ret_type": "tornado.httputil.HTTPHeaders", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "max_redirects": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.max_redirects", "name": "max_redirects", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.method", "name": "method", "type": "builtins.str"}}, "network_interface": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.network_interface", "name": "network_interface", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "prepare_curl_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.prepare_curl_callback", "name": "prepare_curl_callback", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "proxy_auth_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.proxy_auth_mode", "name": "proxy_auth_mode", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "proxy_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.proxy_host", "name": "proxy_host", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "proxy_password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.proxy_password", "name": "proxy_password", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "proxy_port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.proxy_port", "name": "proxy_port", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "proxy_username": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.proxy_username", "name": "proxy_username", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "request_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.request_timeout", "name": "request_timeout", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}}}, "ssl_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.ssl_options", "name": "ssl_options", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "ssl.SSLContext", {".class": "NoneType"}]}}}, "start_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.start_time", "name": "start_time", "type": "builtins.float"}}, "streaming_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.streaming_callback", "name": "streaming_callback", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.url", "name": "url", "type": "builtins.str"}}, "user_agent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.user_agent", "name": "user_agent", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "validate_cert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPRequest.validate_cert", "name": "validate_cert", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.httpclient.HTTPRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.httpclient.HTTPRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.httpclient.HTTPResponse", "name": "HTTPResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.httpclient.HTTPResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.httpclient", "mro": ["tornado.httpclient.HTTPResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "request", "code", "headers", "buffer", "effective_url", "error", "request_time", "time_info", "reason", "start_time"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "request", "code", "headers", "buffer", "effective_url", "error", "request_time", "time_info", "reason", "start_time"], "arg_types": ["tornado.httpclient.HTTPResponse", "tornado.httpclient.HTTPRequest", "builtins.int", {".class": "UnionType", "items": ["tornado.httputil.HTTPHeaders", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["io.BytesIO", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPResponse.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of HTTPResponse", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.HTTPResponse._body", "name": "_body", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}]}}}, "_error_is_response_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tornado.httpclient.HTTPResponse._error_is_response_code", "name": "_error_is_response_code", "type": "builtins.bool"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "tornado.httpclient.HTTPResponse.body", "name": "body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "body of HTTPResponse", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.body", "name": "body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "body of HTTPResponse", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.buffer", "name": "buffer", "type": {".class": "UnionType", "items": ["io.BytesIO", {".class": "NoneType"}]}}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.code", "name": "code", "type": "builtins.int"}}, "effective_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.effective_url", "name": "effective_url", "type": "builtins.str"}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "tornado.httpclient.HTTPResponse.error", "name": "error", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.headers", "name": "headers", "type": "tornado.httputil.HTTPHeaders"}}, "reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.reason", "name": "reason", "type": "builtins.str"}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "tornado.httpclient.HTTPResponse.request", "name": "request", "type": "tornado.httpclient.HTTPRequest"}}, "request_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.request_time", "name": "request_time", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}}}, "rethrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.HTTPResponse.rethrow", "name": "rethrow", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.httpclient.HTTPResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rethrow of HTTPResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "start_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.start_time", "name": "start_time", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}}}, "time_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient.HTTPResponse.time_info", "name": "time_info", "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.httpclient.HTTPResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.httpclient.HTTPResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IOLoop": {".class": "SymbolTableNode", "cross_ref": "tornado.ioloop.IOLoop", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_RequestProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.httpclient._RequestProxy", "name": "_RequestProxy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.httpclient._RequestProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.httpclient", "mro": ["tornado.httpclient._RequestProxy", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient._RequestProxy.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["tornado.httpclient._RequestProxy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of _RequestProxy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "defaults"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient._RequestProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "defaults"], "arg_types": ["tornado.httpclient._RequestProxy", "tornado.httpclient.HTTPRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _RequestProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "defaults": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient._RequestProxy.defaults", "name": "defaults", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.httpclient._RequestProxy.request", "name": "request", "type": "tornado.httpclient.HTTPRequest"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.httpclient._RequestProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.httpclient._RequestProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.httpclient.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.httpclient.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.httpclient.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.httpclient.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.httpclient.__package__", "name": "__package__", "type": "builtins.str"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "future_set_exception_unless_cancelled": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent.future_set_exception_unless_cancelled", "kind": "Gdef"}, "future_set_result_unless_cancelled": {".class": "SymbolTableNode", "cross_ref": "tornado.concurrent.future_set_result_unless_cancelled", "kind": "Gdef"}, "gen": {".class": "SymbolTableNode", "cross_ref": "tornado.gen", "kind": "Gdef"}, "httputil": {".class": "SymbolTableNode", "cross_ref": "tornado.httputil", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.httpclient.main", "name": "main", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "main", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "native_str": {".class": "SymbolTableNode", "cross_ref": "tornado.escape.native_str", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "utf8": {".class": "SymbolTableNode", "cross_ref": "tornado.escape.utf8", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/httpclient.py"}