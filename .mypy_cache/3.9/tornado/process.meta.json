{"data_mtime": 1753508937, "dep_lines": [30, 35, 36, 37, 20, 21, 22, 23, 24, 25, 26, 28, 35, 39, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["tornado.concurrent", "tornado.ioloop", "tornado.iostream", "tornado.log", "asyncio", "os", "multiprocessing", "signal", "subprocess", "sys", "time", "<PERSON><PERSON><PERSON><PERSON>", "tornado", "typing", "builtins", "abc", "asyncio.events", "asyncio.futures", "enum", "tornado.util"], "hash": "322b70e100372b9f051addd383c18b515c3043d23d1d07c1ede381a1cd4442e8", "id": "tornado.process", "ignore_all": true, "interface_hash": "1ddc9a947ac612cc03950880e0485134c29fc203e22e668bcbfe60e02ce1838c", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/process.py", "plugin_data": null, "size": 12696, "suppressed": [], "version_id": "1.8.0"}