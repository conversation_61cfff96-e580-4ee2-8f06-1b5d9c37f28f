{"data_mtime": 1753508937, "dep_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 30], "dependencies": ["tornado.auth", "tornado.autoreload", "tornado.concurrent", "tornado.curl_httpclient", "tornado.escape", "tornado.gen", "tornado.http1connection", "tornado.httpclient", "tornado.httpserver", "tornado.httputil", "tornado.ioloop", "tornado.iostream", "tornado.locale", "tornado.locks", "tornado.log", "tornado.netutil", "tornado.options", "tornado.platform", "tornado.process", "tornado.queues", "tornado.routing", "tornado.simple_httpclient", "tornado.tcpclient", "tornado.tcpserver", "tornado.template", "tornado.testing", "tornado.util", "tornado.web", "typing", "builtins", "abc"], "hash": "45474f2b3367978d964b43d0bc09e11fe664a28b9fc4b125ee5fe36ead3bb1f2", "id": "tornado", "ignore_all": true, "interface_hash": "a64507fa0b63e5947ccc83b5aec7e2f43f80179973e88ec58aebdf01943df088", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/__init__.pyi", "plugin_data": null, "size": 714, "suppressed": [], "version_id": "1.8.0"}