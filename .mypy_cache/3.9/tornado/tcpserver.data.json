{".class": "MypyFile", "_fullname": "tornado.tcpserver", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "IOLoop": {".class": "SymbolTableNode", "cross_ref": "tornado.ioloop.IOLoop", "kind": "Gdef"}, "IOStream": {".class": "SymbolTableNode", "cross_ref": "tornado.iostream.IOStream", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SSLIOStream": {".class": "SymbolTableNode", "cross_ref": "tornado.iostream.SSLIOStream", "kind": "Gdef"}, "TCPServer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tornado.tcpserver.TCPServer", "name": "TCPServer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "tornado.tcpserver.TCPServer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tornado.tcpserver", "mro": ["tornado.tcpserver.TCPServer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "ssl_options", "max_buffer_size", "read_chunk_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "ssl_options", "max_buffer_size", "read_chunk_size"], "arg_types": ["tornado.tcpserver.TCPServer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TCPServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_handle_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "address"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer._handle_connection", "name": "_handle_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "address"], "arg_types": ["tornado.tcpserver.TCPServer", "socket.socket", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_connection of TCPServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.tcpserver.TCPServer._handlers", "name": "_handlers", "type": {".class": "Instance", "args": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "type_ref": "builtins.dict"}}}, "_pending_sockets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.tcpserver.TCPServer._pending_sockets", "name": "_pending_sockets", "type": {".class": "Instance", "args": ["socket.socket"], "type_ref": "builtins.list"}}}, "_sockets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tornado.tcpserver.TCPServer._sockets", "name": "_sockets", "type": {".class": "Instance", "args": ["builtins.int", "socket.socket"], "type_ref": "builtins.dict"}}}, "_started": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.tcpserver.TCPServer._started", "name": "_started", "type": "builtins.bool"}}, "_stopped": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.tcpserver.TCPServer._stopped", "name": "_stopped", "type": "builtins.bool"}}, "add_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "socket"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer.add_socket", "name": "add_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "socket"], "arg_types": ["tornado.tcpserver.TCPServer", "socket.socket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_socket of TCPServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_sockets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sockets"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer.add_sockets", "name": "add_sockets", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sockets"], "arg_types": ["tornado.tcpserver.TCPServer", {".class": "Instance", "args": ["socket.socket"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_sockets of TCPServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "port", "address", "family", "backlog", "flags", "reuse_port"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "port", "address", "family", "backlog", "flags", "reuse_port"], "arg_types": ["tornado.tcpserver.TCPServer", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "socket.AddressFamily", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of TCPServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handle_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "stream", "address"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer.handle_stream", "name": "handle_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "stream", "address"], "arg_types": ["tornado.tcpserver.TCPServer", "tornado.iostream.IOStream", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_stream of TCPServer", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "typing.Awaitable"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "port", "address", "family", "backlog", "flags", "reuse_port"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer.listen", "name": "listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "port", "address", "family", "backlog", "flags", "reuse_port"], "arg_types": ["tornado.tcpserver.TCPServer", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "socket.AddressFamily", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listen of TCPServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "max_buffer_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.tcpserver.TCPServer.max_buffer_size", "name": "max_buffer_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "read_chunk_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.tcpserver.TCPServer.read_chunk_size", "name": "read_chunk_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "ssl_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tornado.tcpserver.TCPServer.ssl_options", "name": "ssl_options", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "ssl.SSLContext", {".class": "NoneType"}]}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "num_processes", "max_restarts"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer.start", "name": "start", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "num_processes", "max_restarts"], "arg_types": ["tornado.tcpserver.TCPServer", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start of TCPServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "tornado.tcpserver.TCPServer.stop", "name": "stop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tornado.tcpserver.TCPServer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop of TCPServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tornado.tcpserver.TCPServer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tornado.tcpserver.TCPServer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_DEFAULT_BACKLOG": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil._DEFAULT_BACKLOG", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.tcpserver.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.tcpserver.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.tcpserver.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.tcpserver.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tornado.tcpserver.__package__", "name": "__package__", "type": "builtins.str"}}, "add_accept_handler": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil.add_accept_handler", "kind": "Gdef"}, "app_log": {".class": "SymbolTableNode", "cross_ref": "tornado.log.app_log", "kind": "Gdef"}, "bind_sockets": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil.bind_sockets", "kind": "Gdef"}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef"}, "errno_from_exception": {".class": "SymbolTableNode", "cross_ref": "tornado.util.errno_from_exception", "kind": "Gdef"}, "gen": {".class": "SymbolTableNode", "cross_ref": "tornado.gen", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "process": {".class": "SymbolTableNode", "cross_ref": "tornado.process", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "ssl_wrap_socket": {".class": "SymbolTableNode", "cross_ref": "tornado.netutil.ssl_wrap_socket", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/tcpserver.py"}