{"data_mtime": 1753508936, "dep_lines": [31, 34, 35, 30, 32, 43, 47, 1, 1, 1, 1, 1, 1, 38], "dep_prios": [10, 5, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 10], "dependencies": ["logging.handlers", "tornado.escape", "tornado.util", "logging", "sys", "curses", "typing", "builtins", "_collections_abc", "_curses", "abc", "types", "typing_extensions"], "hash": "4628fbcbf780652f7426ae11021a8a2027bc10096ba6c16d93c9e02b4db09c57", "id": "tornado.log", "ignore_all": true, "interface_hash": "5e7078a321cd46e4534eb5180c30b7c70a48f7e32c4e683045bcead53069976c", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/log.py", "plugin_data": null, "size": 12547, "suppressed": ["colorama"], "version_id": "1.8.0"}