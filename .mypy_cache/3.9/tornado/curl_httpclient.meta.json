{"data_mtime": 1753508937, "dep_lines": [27, 28, 30, 31, 38, 18, 19, 20, 22, 23, 24, 25, 27, 40, 1, 1, 1, 1, 1, 1, 21], "dep_prios": [10, 10, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 20, 5, 5, 30, 30, 30, 30, 30, 10], "dependencies": ["tornado.httputil", "tornado.ioloop", "tornado.escape", "tornado.httpclient", "tornado.log", "collections", "functools", "logging", "re", "threading", "time", "io", "tornado", "typing", "builtins", "abc", "ast", "datetime", "tornado.util", "typing_extensions"], "hash": "7789805663cc1f936df329732b90f4464332e99d03db292d5f53cfc5ae267b8a", "id": "tornado.curl_httpclient", "ignore_all": true, "interface_hash": "caab568dc499994f7a22918c81882e9bbf0ac12e4cb067cb2c41ead523d80cb4", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/curl_httpclient.py", "plugin_data": null, "size": 24904, "suppressed": ["pycurl"], "version_id": "1.8.0"}