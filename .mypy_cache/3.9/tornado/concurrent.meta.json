{"data_mtime": 1753508936, "dep_lines": [29, 34, 28, 29, 30, 31, 32, 36, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures", "tornado.log", "asyncio", "concurrent", "functools", "sys", "types", "typing", "builtins", "_typeshed", "abc", "asyncio.events", "asyncio.futures", "concurrent.futures._base"], "hash": "9f468edcb2c46d8ac1e275d4be63507317a786511f2ec1bb237f76d4974cfd68", "id": "tornado.concurrent", "ignore_all": true, "interface_hash": "116051bfe366bcf04fc423ef62d5872bd0ff4ef3c54f6875a908a4db8b585c87", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tornado/concurrent.py", "plugin_data": null, "size": 8376, "suppressed": [], "version_id": "1.8.0"}