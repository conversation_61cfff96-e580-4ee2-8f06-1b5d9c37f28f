{".class": "MypyFile", "_fullname": "httpx._main", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Client": {".class": "SymbolTableNode", "cross_ref": "httpx._client.Client", "kind": "Gdef"}, "RequestError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.RequestError", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Response", "kind": "Gdef"}, "_PCTRTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpx._main._PCTRTT", "line": 189, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.tuple"}}}, "_PCTRTTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpx._main._PCTRTTT", "line": 190, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._main._PCTRTT"}], "type_ref": "builtins.tuple"}}}, "_PeerCertRetDictType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpx._main._PeerCertRetDictType", "line": 191, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "httpx._main._PCTRTTT"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._main._PCTRTT"}]}], "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._main.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._main.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._main.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._main.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._main.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "click": {".class": "SymbolTableNode", "cross_ref": "click", "kind": "Gdef"}, "codes": {".class": "SymbolTableNode", "cross_ref": "httpx._status_codes.codes", "kind": "Gdef"}, "download_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["response", "download"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.download_response", "name": "download_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["response", "download"], "arg_types": ["httpx._models.Response", "typing.BinaryIO"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "download_response", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format_certificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cert"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.format_certificate", "name": "format_certificate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cert"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._main._PeerCertRetDictType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_certificate", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format_request_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "http2"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.format_request_headers", "name": "format_request_headers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "http2"], "arg_types": ["httpcore._models.Request", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_request_headers", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format_response_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["http_version", "status", "reason_phrase", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.format_response_headers", "name": "format_response_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["http_version", "status", "reason_phrase", "headers"], "arg_types": ["builtins.bytes", "builtins.int", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_response_headers", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_lexer_for_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.get_lexer_for_response", "name": "get_lexer_for_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response"], "arg_types": ["httpx._models.Response"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_lexer_for_response", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handle_help": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "param", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.handle_help", "name": "handle_help", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "param", "value"], "arg_types": ["click.core.Context", {".class": "UnionType", "items": ["click.core.Option", "click.core.Parameter"]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_help", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "httpcore": {".class": "SymbolTableNode", "cross_ref": "httpcore", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["url", "method", "params", "content", "data", "files", "json", "headers", "cookies", "auth", "proxy", "timeout", "follow_redirects", "verify", "http2", "download", "verbose"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "httpx._main.main", "name": "main", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["url", "method", "params", "content", "data", "files", "json", "headers", "cookies", "auth", "proxy", "timeout", "follow_redirects", "verify", "http2", "download", "verbose"], "arg_types": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "click.types.File"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, "builtins.str", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["typing.BinaryIO", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "main", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "httpx._main.main", "name": "main", "type": "click.core.Command"}}}, "print_help": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.print_help", "name": "print_help", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_help", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "print_request_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "http2"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.print_request_headers", "name": "print_request_headers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "http2"], "arg_types": ["httpcore._models.Request", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_request_headers", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "print_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.print_response", "name": "print_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response"], "arg_types": ["httpx._models.Response"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_response", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "print_response_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["http_version", "status", "reason_phrase", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.print_response_headers", "name": "print_response_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["http_version", "status", "reason_phrase", "headers"], "arg_types": ["builtins.bytes", "builtins.int", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_response_headers", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pygments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "httpx._main.pygments", "name": "pygments", "type": {".class": "AnyType", "missing_import_name": "httpx._main.pygments", "source_any": null, "type_of_any": 3}}}, "rich": {".class": "SymbolTableNode", "cross_ref": "rich", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "trace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["name", "info", "verbose"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.trace", "name": "trace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["name", "info", "verbose"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trace", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "validate_auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "param", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.validate_auth", "name": "validate_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "param", "value"], "arg_types": ["click.core.Context", {".class": "UnionType", "items": ["click.core.Option", "click.core.Parameter"]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_auth", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "validate_json": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "param", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._main.validate_json", "name": "validate_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "param", "value"], "arg_types": ["click.core.Context", {".class": "UnionType", "items": ["click.core.Option", "click.core.Parameter"]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_json", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpx/_main.py"}