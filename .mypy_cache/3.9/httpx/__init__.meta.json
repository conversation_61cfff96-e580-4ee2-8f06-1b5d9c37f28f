{"data_mtime": 1753508938, "dep_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["httpx.__version__", "httpx._api", "httpx._auth", "httpx._client", "httpx._config", "httpx._content", "httpx._exceptions", "httpx._models", "httpx._status_codes", "httpx._transports", "httpx._types", "httpx._urls", "httpx._main", "builtins", "abc", "ast", "typing", "typing_extensions"], "hash": "0ac6997bac998f4ac783adf6d8058a587193315afdb718047c3e4fdff46bcfad", "id": "httpx", "ignore_all": true, "interface_hash": "448046ef199611119b5fec15c298047af53835ab259225e5d1c759625b9a11f3", "mtime": 1753467212, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpx/__init__.py", "plugin_data": null, "size": 2171, "suppressed": [], "version_id": "1.8.0"}