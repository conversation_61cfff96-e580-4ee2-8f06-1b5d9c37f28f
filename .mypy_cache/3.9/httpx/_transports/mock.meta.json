{"data_mtime": 1753508934, "dep_lines": [6, 5, 1, 3, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30], "dependencies": ["httpx._transports.base", "httpx._models", "__future__", "typing", "builtins", "abc"], "hash": "3d3a34779ebb4484d7c46ae48ba90deffebbc300317f088c0dcb972626428c4a", "id": "httpx._transports.mock", "ignore_all": true, "interface_hash": "03cc0c8decee45e94a7ca9d0f7d785ee39afd401c0067db74698d06c13fc6b31", "mtime": 1753467212, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpx/_transports/mock.py", "plugin_data": null, "size": 1232, "suppressed": [], "version_id": "1.8.0"}