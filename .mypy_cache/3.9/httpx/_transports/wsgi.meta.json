{"data_mtime": 1753508934, "dep_lines": [10, 8, 9, 14, 1, 3, 4, 5, 6, 13, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 10, 10, 10, 10, 25, 5, 30], "dependencies": ["httpx._transports.base", "httpx._models", "httpx._types", "_typeshed.wsgi", "__future__", "io", "itertools", "sys", "typing", "_typeshed", "builtins", "abc"], "hash": "35c3d7dd76a9fc4c0215958efce692c9036e227098c7540c35b3bc1807a2ea36", "id": "httpx._transports.wsgi", "ignore_all": true, "interface_hash": "3ff1cb253694e9ac2230812e4230fc1a4e9630ca224b3e43f869a160985ed7d0", "mtime": 1753467212, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpx/_transports/wsgi.py", "plugin_data": null, "size": 4825, "suppressed": [], "version_id": "1.8.0"}