{".class": "MypyFile", "_fullname": "httpx._transports.asgi", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGIResponseStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._types.AsyncByteStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._transports.asgi.ASGIResponseStream", "name": "ASGIResponseStream", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._transports.asgi.ASGIResponseStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._transports.asgi", "mro": ["httpx._transports.asgi.ASGIResponseStream", "httpx._types.AsyncByteStream", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "httpx._transports.asgi.ASGIResponseStream.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._transports.asgi.ASGIResponseStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of ASGIResponseStream", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "typing.AsyncIterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.asgi.ASGIResponseStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["httpx._transports.asgi.ASGIResponseStream", {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ASGIResponseStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.asgi.ASGIResponseStream._body", "name": "_body", "type": {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.asgi.ASGIResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._transports.asgi.ASGIResponseStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ASGITransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._transports.base.AsyncBaseTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._transports.asgi.ASGITransport", "name": "ASGITransport", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._transports.asgi.ASGITransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._transports.asgi", "mro": ["httpx._transports.asgi.ASGITransport", "httpx._transports.base.AsyncBaseTransport", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "app", "raise_app_exceptions", "root_path", "client"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.asgi.ASGITransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "app", "raise_app_exceptions", "root_path", "client"], "arg_types": ["httpx._transports.asgi.ASGITransport", {".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.asgi._ASGIApp"}, "builtins.bool", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ASGITransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.asgi.ASGITransport.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.MutableMapping"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.asgi._Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.asgi._Send"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.asgi.ASGITransport.client", "name": "client", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "handle_async_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpx._transports.asgi.ASGITransport.handle_async_request", "name": "handle_async_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._transports.asgi.ASGITransport", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_async_request of ASGITransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "httpx._models.Response"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "raise_app_exceptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.asgi.ASGITransport.raise_app_exceptions", "name": "raise_app_exceptions", "type": "builtins.bool"}}, "root_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.asgi.ASGITransport.root_path", "name": "root_path", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.asgi.ASGITransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._transports.asgi.ASGITransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncBaseTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.base.AsyncBaseTransport", "kind": "Gdef", "module_public": false}, "AsyncByteStream": {".class": "SymbolTableNode", "cross_ref": "httpx._types.AsyncByteStream", "kind": "Gdef", "module_public": false}, "Event": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "httpx._transports.asgi.Event", "line": 14, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["asyncio.locks.Event", {".class": "AnyType", "missing_import_name": "httpx._transports.asgi.trio", "source_any": null, "type_of_any": 3}]}}}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Request", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Response", "kind": "Gdef", "module_public": false}, "_ASGIApp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpx._transports.asgi._ASGIApp", "line": 22, "no_args": false, "normalized": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.MutableMapping"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.asgi._Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.asgi._Send"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_Message": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpx._transports.asgi._Message", "line": 17, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.MutableMapping"}}}, "_Receive": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpx._transports.asgi._Receive", "line": 18, "no_args": false, "normalized": false, "target": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.asgi._Message"}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_Send": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpx._transports.asgi._Send", "line": 19, "no_args": false, "normalized": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.MutableMapping"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx._transports.asgi.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.asgi.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.asgi.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.asgi.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.asgi.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.asgi.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "create_event": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.asgi.create_event", "name": "create_event", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_event", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.asgi.Event"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_running_trio": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.asgi.is_running_trio", "name": "is_running_trio", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_running_trio", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "trio": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "httpx._transports.asgi.trio", "name": "trio", "type": {".class": "AnyType", "missing_import_name": "httpx._transports.asgi.trio", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpx/_transports/asgi.py"}