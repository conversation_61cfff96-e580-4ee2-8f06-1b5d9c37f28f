{"data_mtime": 1753508935, "dep_lines": [7, 5, 6, 1, 3, 10, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 5, 10, 25, 5, 30, 30, 30, 25], "dependencies": ["httpx._transports.base", "httpx._models", "httpx._types", "__future__", "typing", "asyncio", "builtins", "abc", "asyncio.locks", "typing_extensions"], "hash": "1d17e20d830fb78c101f6805807660e1cfa2dec6e5a3a6cbe464ea7044fec73f", "id": "httpx._transports.asgi", "ignore_all": true, "interface_hash": "6871359dfe470ae53a5e57fc3da7698d73de4a139ac4a6ec03044490d3568e98", "mtime": 1753467212, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpx/_transports/asgi.py", "plugin_data": null, "size": 5501, "suppressed": ["trio"], "version_id": "1.8.0"}