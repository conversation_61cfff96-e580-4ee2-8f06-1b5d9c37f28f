{"data_mtime": 1753508938, "dep_lines": [58, 38, 39, 55, 56, 57, 27, 29, 30, 31, 34, 36, 150, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 189], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 25, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["httpx._transports.base", "httpx._config", "httpx._exceptions", "httpx._models", "httpx._types", "httpx._urls", "__future__", "contextlib", "typing", "types", "ssl", "httpx", "httpcore", "builtins", "abc", "httpcore._async", "httpcore._async.connection_pool", "httpcore._async.http_proxy", "httpcore._async.interfaces", "httpcore._async.socks_proxy", "httpcore._backends", "httpcore._backends.base", "httpcore._models", "httpcore._sync", "httpcore._sync.connection_pool", "httpcore._sync.http_proxy", "httpcore._sync.interfaces", "httpcore._sync.socks_proxy", "typing_extensions"], "hash": "03379a454c95c0271c4f2c8d25ec437f49f57457f3cf2769748420bf0ecf2e79", "id": "httpx._transports.default", "ignore_all": true, "interface_hash": "745d0d1bd9b573063265b66fcdc2db7805582c52b82482c60ba6263100208be8", "mtime": 1753467212, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpx/_transports/default.py", "plugin_data": null, "size": 13983, "suppressed": ["<PERSON><PERSON>"], "version_id": "1.8.0"}