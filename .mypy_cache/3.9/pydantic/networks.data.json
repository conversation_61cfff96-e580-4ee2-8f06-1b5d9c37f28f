{".class": "MypyFile", "_fullname": "pydantic.networks", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AmqpDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.AmqpDsn", "name": "AmqpDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.AmqpDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.AmqpDsn", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.AmqpDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.AmqpDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.AmqpDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AnyHttpUrl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.AnyHttpUrl", "name": "AnyHttpUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.AnyHttpUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.AnyHttpUrl", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.AnyHttpUrl._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.AnyHttpUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.AnyHttpUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnyUrl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks._BaseUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.AnyUrl", "name": "AnyUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.AnyUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.AnyUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.AnyUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnyWebsocketUrl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.AnyWebsocketUrl", "name": "AnyWebsocketUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.AnyWebsocketUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.AnyWebsocketUrl", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.AnyWebsocketUrl._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.AnyWebsocketUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.AnyWebsocketUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_public": false}, "ClickHouseDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.ClickHouseDsn", "name": "ClickHouseDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.ClickHouseDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.ClickHouseDsn", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.ClickHouseDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.ClickHouseDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.ClickHouseDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CockroachDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.CockroachDsn", "name": "CockroachDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.CockroachDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.CockroachDsn", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.CockroachDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks.CockroachDsn.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.CockroachDsn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of CockroachDsn", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks.CockroachDsn.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.CockroachDsn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of CockroachDsn", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.CockroachDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.CockroachDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EmailStr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.networks.EmailStr", "line": 954, "no_args": true, "normalized": false, "target": "builtins.str"}}, "FileUrl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.FileUrl", "name": "FileUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.FileUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.FileUrl", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.FileUrl._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.FileUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.FileUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FtpUrl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.FtpUrl", "name": "FtpUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.FtpUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.FtpUrl", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.FtpUrl._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.FtpUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.FtpUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetCoreSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetCoreSchemaHandler", "kind": "Gdef", "module_public": false}, "HttpUrl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.HttpUrl", "name": "HttpUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.HttpUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.HttpUrl", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.HttpUrl._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.HttpUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.HttpUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPv4Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Address", "kind": "Gdef", "module_public": false}, "IPv4Interface": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Interface", "kind": "Gdef", "module_public": false}, "IPv4Network": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Network", "kind": "Gdef", "module_public": false}, "IPv6Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Address", "kind": "Gdef", "module_public": false}, "IPv6Interface": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Interface", "kind": "Gdef", "module_public": false}, "IPv6Network": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Network", "kind": "Gdef", "module_public": false}, "IPvAnyAddress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.networks.IPvAnyAddress", "line": 1096, "no_args": false, "normalized": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.networks.IPvAnyAddressType"}}}, "IPvAnyAddressType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.networks.IPvAnyAddressType", "line": 1091, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"]}}}, "IPvAnyInterface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.networks.IPvAnyInterface", "line": 1097, "no_args": false, "normalized": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.networks.IPvAnyInterfaceType"}}}, "IPvAnyInterfaceType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.networks.IPvAnyInterfaceType", "line": 1092, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["ipaddress.IPv4Interface", "ipaddress.IPv6Interface"]}}}, "IPvAnyNetwork": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.networks.IPvAnyNetwork", "line": 1098, "no_args": false, "normalized": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.networks.IPvAnyNetworkType"}}}, "IPvAnyNetworkType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.networks.IPvAnyNetworkType", "line": 1093, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["ipaddress.IPv4Network", "ipaddress.IPv6Network"]}}}, "JsonSchemaValue": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaValue", "kind": "Gdef", "module_public": false}, "KafkaDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.KafkaDsn", "name": "KafkaDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.KafkaDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.KafkaDsn", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.KafkaDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.KafkaDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.KafkaDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MAX_EMAIL_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.MAX_EMAIL_LENGTH", "name": "MAX_EMAIL_LENGTH", "type": "builtins.int"}}, "MariaDBDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.MariaDBDsn", "name": "MariaDBDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.MariaDBDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.MariaDBDsn", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.MariaDBDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.MariaDBDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.MariaDBDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MongoDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks._BaseMultiHostUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.MongoDsn", "name": "MongoDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.MongoDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.MongoDsn", "pydantic.networks._BaseMultiHostUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.MongoDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.MongoDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.MongoDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultiHostHost": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.MultiHostHost", "kind": "Gdef", "module_public": false}, "MySQLDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.MySQLDsn", "name": "MySQLDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.MySQLDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.MySQLDsn", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.MySQLDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.MySQLDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.MySQLDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NameEmail": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic._internal._repr.Representation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.NameEmail", "name": "NameEmail", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.NameEmail", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.NameEmail", "pydantic._internal._repr.Representation", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks.NameEmail.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.networks.NameEmail", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of NameEmail", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "_source", "_handler"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks.NameEmail.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "_source", "_handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks.NameEmail"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of NameEmail", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks.NameEmail.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "_source", "_handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks.NameEmail"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of NameEmail", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "__get_pydantic_json_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks.NameEmail.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks.NameEmail"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of NameEmail", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks.NameEmail.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks.NameEmail"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of NameEmail", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "email"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks.NameEmail.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "email"], "arg_types": ["pydantic.networks.NameEmail", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NameEmail", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic.networks.NameEmail.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks.NameEmail.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.networks.NameEmail"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of NameEmail", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks.NameEmail._validate", "name": "_validate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}, "builtins.str"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate of NameEmail", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks.NameEmail._validate", "name": "_validate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}, "builtins.str"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate of NameEmail", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}]}}}}, "email": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.networks.NameEmail.email", "name": "email", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.networks.NameEmail.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NameEmail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NameEmail", "values": [], "variance": 0}, "slots": ["email", "name"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NatsDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks._BaseMultiHostUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.NatsDsn", "name": "NatsDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.NatsDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.NatsDsn", "pydantic.networks._BaseMultiHostUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.NatsDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.NatsDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.NatsDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NetworkType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.networks.NetworkType", "line": 35, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.int", {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.int"]}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}}}, "PostgresDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks._BaseMultiHostUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.PostgresDsn", "name": "PostgresDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.PostgresDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.PostgresDsn", "pydantic.networks._BaseMultiHostUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.PostgresDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks.PostgresDsn.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.PostgresDsn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of PostgresDsn", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks.PostgresDsn.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.PostgresDsn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of PostgresDsn", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.PostgresDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.PostgresDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticCustomError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticCustomError", "kind": "Gdef", "module_public": false}, "PydanticSerializationUnexpectedValue": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticSerializationUnexpectedValue", "kind": "Gdef", "module_public": false}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef", "module_public": false}, "RedisDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.RedisDsn", "name": "RedisDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.RedisDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.RedisDsn", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.RedisDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks.RedisDsn.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.RedisDsn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of RedisDsn", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks.RedisDsn.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.RedisDsn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of RedisDsn", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.RedisDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.RedisDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SchemaSerializer": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaSerializer", "kind": "Gdef", "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_public": false}, "SnowflakeDsn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.SnowflakeDsn", "name": "SnowflakeDsn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.SnowflakeDsn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.SnowflakeDsn", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.SnowflakeDsn._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks.SnowflakeDsn.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.SnowflakeDsn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of SnowflakeDsn", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks.SnowflakeDsn.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.SnowflakeDsn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of SnowflakeDsn", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.SnowflakeDsn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.SnowflakeDsn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TypeAdapter": {".class": "SymbolTableNode", "cross_ref": "pydantic.type_adapter.TypeAdapter", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_public": false}, "UrlConstraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.UrlConstraints", "name": "UrlConstraints", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.UrlConstraints", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 83, "name": "max_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 84, "name": "allowed_schemes", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 85, "name": "host_required", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 86, "name": "default_host", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 87, "name": "default_port", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 88, "name": "default_path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.UrlConstraints", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.networks.UrlConstraints.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source", "handler"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks.UrlConstraints.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source", "handler"], "arg_types": ["pydantic.networks.UrlConstraints", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of UrlConstraints", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks.UrlConstraints.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.UrlConstraints"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of UrlConstraints", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks.UrlConstraints.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path"], "arg_types": ["pydantic.networks.UrlConstraints", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UrlConstraints", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.networks.UrlConstraints.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of UrlConstraints", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.networks.UrlConstraints.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of UrlConstraints", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "allowed_schemes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.networks.UrlConstraints.allowed_schemes", "name": "allowed_schemes", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}, "default_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.networks.UrlConstraints.default_host", "name": "default_host", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "default_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.networks.UrlConstraints.default_path", "name": "default_path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "default_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.networks.UrlConstraints.default_port", "name": "default_port", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "defined_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks.UrlConstraints.defined_constraints", "name": "defined_constraints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.UrlConstraints"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defined_constraints of UrlConstraints", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks.UrlConstraints.defined_constraints", "name": "defined_constraints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks.UrlConstraints"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defined_constraints of UrlConstraints", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "host_required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.networks.UrlConstraints.host_required", "name": "host_required", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "max_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.networks.UrlConstraints.max_length", "name": "max_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.UrlConstraints.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.UrlConstraints", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WebsocketUrl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.networks.AnyUrl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks.WebsocketUrl", "name": "WebsocketUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks.WebsocketUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks.WebsocketUrl", "pydantic.networks.AnyUrl", "pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.WebsocketUrl._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks.WebsocketUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks.WebsocketUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseMultiHostUrl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks._BaseMultiHostUrl", "name": "_BaseMultiHostUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks._BaseMultiHostUrl", "builtins.object"], "names": {".class": "SymbolTable", "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__deepcopy__ of _BaseMultiHostUrl", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}]}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.networks._BaseMultiHostUrl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of _BaseMultiHostUrl", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "source", "handler"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks._BaseMultiHostUrl.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "source", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks._BaseMultiHostUrl"}, {".class": "TypeType", "item": "pydantic.networks._BaseMultiHostUrl"}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of _BaseMultiHostUrl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseMultiHostUrl.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "source", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks._BaseMultiHostUrl"}, {".class": "TypeType", "item": "pydantic.networks._BaseMultiHostUrl"}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of _BaseMultiHostUrl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "__get_pydantic_json_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks._BaseMultiHostUrl.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks._BaseMultiHostUrl"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of _BaseMultiHostUrl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseMultiHostUrl.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks._BaseMultiHostUrl"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of _BaseMultiHostUrl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of _BaseMultiHostUrl", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["pydantic.networks._BaseMultiHostUrl", {".class": "UnionType", "items": ["builtins.str", "pydantic_core._pydantic_core.MultiHostUrl", "pydantic.networks._BaseMultiHostUrl"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _BaseMultiHostUrl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _BaseMultiHostUrl", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__pydantic_serializer__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks._BaseMultiHostUrl.__pydantic_serializer__", "name": "__pydantic_serializer__", "type": "pydantic_core._pydantic_core.SchemaSerializer"}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of _BaseMultiHostUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of _BaseMultiHostUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.networks._BaseMultiHostUrl._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}, "_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.networks._BaseMultiHostUrl._url", "name": "_url", "type": "pydantic_core._pydantic_core.MultiHostUrl"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "scheme", "hosts", "username", "password", "host", "port", "path", "query", "fragment"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks._BaseMultiHostUrl.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "scheme", "hosts", "username", "password", "host", "port", "path", "query", "fragment"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.MultiHostHost"}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of _BaseMultiHostUrl", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseMultiHostUrl.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "scheme", "hosts", "username", "password", "host", "port", "path", "query", "fragment"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.MultiHostHost"}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of _BaseMultiHostUrl", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}]}}}}, "encoded_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.encoded_string", "name": "encoded_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encoded_string of _BaseMultiHostUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fragment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseMultiHostUrl.fragment", "name": "fragment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fragment of _BaseMultiHostUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseMultiHostUrl.fragment", "name": "fragment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fragment of _BaseMultiHostUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "hosts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.hosts", "name": "hosts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hosts of _BaseMultiHostUrl", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.MultiHostHost"}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseMultiHostUrl.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of _BaseMultiHostUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseMultiHostUrl.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of _BaseMultiHostUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseMultiHostUrl.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of _BaseMultiHostUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseMultiHostUrl.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of _BaseMultiHostUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "query_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.query_params", "name": "query_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query_params of _BaseMultiHostUrl", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseMultiHostUrl.scheme", "name": "scheme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scheme of _BaseMultiHostUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseMultiHostUrl.scheme", "name": "scheme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scheme of _BaseMultiHostUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "serialize_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "url", "info"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks._BaseMultiHostUrl.serialize_url", "name": "serialize_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "url", "info"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_url of _BaseMultiHostUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseMultiHostUrl.serialize_url", "name": "serialize_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "url", "info"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_url of _BaseMultiHostUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}]}}}}, "unicode_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseMultiHostUrl.unicode_string", "name": "unicode_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseMultiHostUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_string of _BaseMultiHostUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseMultiHostUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseMultiHostUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseUrl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.networks._BaseUrl", "name": "_BaseUrl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.networks._BaseUrl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.networks", "mro": ["pydantic.networks._BaseUrl", "builtins.object"], "names": {".class": "SymbolTable", "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__deepcopy__ of _BaseUrl", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}]}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.networks._BaseUrl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of _BaseUrl", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.networks._BaseUrl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of _BaseUrl", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "source", "handler"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "source", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks._BaseUrl"}, {".class": "TypeType", "item": "pydantic.networks._BaseUrl"}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of _BaseUrl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "source", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks._BaseUrl"}, {".class": "TypeType", "item": "pydantic.networks._BaseUrl"}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of _BaseUrl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "__get_pydantic_json_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks._BaseUrl"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of _BaseUrl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "core_schema", "handler"], "arg_types": [{".class": "TypeType", "item": "pydantic.networks._BaseUrl"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of _BaseUrl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.networks._BaseUrl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of _BaseUrl", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of _BaseUrl", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["pydantic.networks._BaseUrl", {".class": "UnionType", "items": ["builtins.str", "pydantic_core._pydantic_core.Url", "pydantic.networks._BaseUrl"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _BaseUrl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.networks._BaseUrl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of _BaseUrl", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _BaseUrl", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.networks._BaseUrl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of _BaseUrl", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__pydantic_serializer__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.networks._BaseUrl.__pydantic_serializer__", "name": "__pydantic_serializer__", "type": "pydantic_core._pydantic_core.SchemaSerializer"}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of _BaseUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of _BaseUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.networks._BaseUrl._constraints", "name": "_constraints", "type": "pydantic.networks.UrlConstraints"}}, "_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.networks._BaseUrl._url", "name": "_url", "type": "pydantic_core._pydantic_core.Url"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["cls", "scheme", "username", "password", "host", "port", "path", "query", "fragment"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["cls", "scheme", "username", "password", "host", "port", "path", "query", "fragment"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of _BaseUrl", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 3, 5, 5, 5, 5], "arg_names": ["cls", "scheme", "username", "password", "host", "port", "path", "query", "fragment"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of _BaseUrl", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}]}}}}, "encoded_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.encoded_string", "name": "encoded_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encoded_string of _BaseUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fragment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.fragment", "name": "fragment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fragment of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.fragment", "name": "fragment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fragment of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.password", "name": "password", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "password of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.password", "name": "password", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "password of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.port", "name": "port", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "port of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.port", "name": "port", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "port of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "query_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.query_params", "name": "query_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query_params of _BaseUrl", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.scheme", "name": "scheme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scheme of _BaseUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.scheme", "name": "scheme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scheme of _BaseUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "serialize_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "url", "info"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.serialize_url", "name": "serialize_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "url", "info"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_url of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.serialize_url", "name": "serialize_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "url", "info"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_url of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}]}}}}, "unicode_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.unicode_host", "name": "unicode_host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_host of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "unicode_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._BaseUrl.unicode_string", "name": "unicode_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_string of _BaseUrl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "username": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.networks._BaseUrl.username", "name": "username", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "username of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.networks._BaseUrl.username", "name": "username", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.networks._BaseUrl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "username of _BaseUrl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.networks._BaseUrl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.networks._BaseUrl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CoreMultiHostUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.MultiHostUrl", "kind": "Gdef", "module_public": false}, "_CoreUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.Url", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.networks.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.networks.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.networks.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.networks.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.networks.__package__", "name": "__package__", "type": "builtins.str"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "_build_pretty_email_regex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks._build_pretty_email_regex", "name": "_build_pretty_email_regex", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_pretty_email_regex", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_build_type_adapter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic.networks._build_type_adapter", "name": "_build_type_adapter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.networks._BaseUrl"}, {".class": "TypeType", "item": "pydantic.networks._BaseMultiHostUrl"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_type_adapter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "pydantic.type_adapter.TypeAdapter"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.networks._build_type_adapter", "name": "_build_type_adapter", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "pydantic.type_adapter.TypeAdapter"}], "type_ref": "functools._lru_cache_wrapper"}}}}, "_dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "_repr": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._repr", "kind": "Gdef", "module_public": false}, "_schema_generation_shared": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._schema_generation_shared", "kind": "Gdef", "module_public": false}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef", "module_public": false}, "email_validator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pydantic.networks.email_validator", "name": "email_validator", "type": {".class": "AnyType", "missing_import_name": "pydantic.networks.email_validator", "source_any": null, "type_of_any": 3}}}, "fields": {".class": "SymbolTableNode", "cross_ref": "dataclasses.fields", "kind": "Gdef", "module_public": false}, "getattr_migration": {".class": "SymbolTableNode", "cross_ref": "pydantic._migration.getattr_migration", "kind": "Gdef", "module_public": false}, "import_email_validator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks.import_email_validator", "name": "import_email_validator", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_email_validator", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef", "module_public": false}, "pretty_email_regex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.networks.pretty_email_regex", "name": "pretty_email_regex", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "validate_email": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.networks.validate_email", "name": "validate_email", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_email", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "cross_ref": "importlib.metadata.version", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/networks.py"}