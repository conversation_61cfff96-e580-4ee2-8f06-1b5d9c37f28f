{"data_mtime": 1753508935, "dep_lines": [15, 16, 17, 18, 19, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._import_utils", "pydantic.color", "pydantic.networks", "pydantic.types", "pydantic.warnings", "datetime", "warnings", "collections", "decimal", "enum", "ipaddress", "pathlib", "re", "types", "typing", "uuid", "typing_extensions", "builtins", "_decimal", "abc", "os", "pydantic._internal", "pydantic._internal._repr", "pydantic.v1", "pydantic.v1.json"], "hash": "1e55821b7e5146bc72cee4d2e8b4d089907046198365699ea901fcacb5bac00f", "id": "pydantic.deprecated.json", "ignore_all": true, "interface_hash": "d4b507b6b3c526742eab295c12bc011e839019422f7ea82f9e3f01c8b6f3fae2", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/deprecated/json.py", "plugin_data": null, "size": 4657, "suppressed": [], "version_id": "1.8.0"}