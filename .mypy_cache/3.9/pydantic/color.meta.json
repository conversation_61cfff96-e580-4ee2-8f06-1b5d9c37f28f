{"data_mtime": 1753508935, "dep_lines": [23, 24, 20, 23, 25, 26, 15, 16, 17, 18, 20, 21, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 5, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "pydantic_core.core_schema", "pydantic._internal", "pydantic.json_schema", "pydantic.warnings", "math", "re", "colorsys", "typing", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_typeshed", "abc", "pydantic.annotated_handlers", "pydantic.v1", "pydantic.v1.color"], "hash": "033a867d540717ddbf65cb4372e7b40cce324e9d8fead7a49304483535ab2c8a", "id": "pydantic.color", "ignore_all": true, "interface_hash": "a2d8d3064f957426e506667453ab572c1ead04f47d9b0a421895d6877f1bade8", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/color.py", "plugin_data": null, "size": 21481, "suppressed": [], "version_id": "1.8.0"}