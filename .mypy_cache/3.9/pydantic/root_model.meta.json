{"data_mtime": 1753508935, "dep_lines": [11, 11, 11, 12, 19, 3, 5, 6, 8, 10, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 25, 5, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic._internal", "pydantic.main", "pydantic.fields", "__future__", "typing", "copy", "pydantic_core", "pydantic", "typing_extensions", "builtins", "_typeshed", "abc", "annotated_types", "pydantic._internal._generics", "pydantic.aliases", "pydantic.types", "pydantic_core._pydantic_core", "re"], "hash": "4825e1a510a06607ea13d0065494c2ee4300a2329f7cbecf5788b4a9cc0e326d", "id": "pydantic.root_model", "ignore_all": true, "interface_hash": "0b5deeb350b7fb80a878474c21008310b7e2678d020e3823e617e49c26d7e71e", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/root_model.py", "plugin_data": null, "size": 6279, "suppressed": [], "version_id": "1.8.0"}