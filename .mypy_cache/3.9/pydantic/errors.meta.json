{"data_mtime": 1753508935, "dep_lines": [11, 9, 11, 13, 14, 3, 5, 6, 8, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 5, 10, 5, 5, 5, 30], "dependencies": ["pydantic._internal._repr", "typing_inspection.introspection", "pydantic._internal", "pydantic._migration", "pydantic.version", "__future__", "re", "typing", "typing_extensions", "builtins", "abc"], "hash": "edcb41342b6de7b919171ef52ecd87f3a22e7d0011bf8c0f29ff0385db159f9c", "id": "pydantic.errors", "ignore_all": true, "interface_hash": "68b6a1784b8009a6ca777d229be41411667cb6aae15dc63643a016e23b953fd3", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/errors.py", "plugin_data": null, "size": 6002, "suppressed": [], "version_id": "1.8.0"}