{"data_mtime": 1753508935, "dep_lines": [9, 12, 13, 1, 3, 4, 5, 7, 1, 1, 1], "dep_prios": [5, 25, 25, 5, 10, 5, 5, 5, 5, 30, 30], "dependencies": ["pydantic._internal._utils", "pydantic.config", "pydantic.fields", "__future__", "dataclasses", "inspect", "typing", "pydantic_core", "builtins", "abc", "pydantic._internal._repr"], "hash": "f049633c97b8a529daa6e8ab1b90e4040803d618201f1100cb314f1fef47d331", "id": "pydantic._internal._signature", "ignore_all": true, "interface_hash": "a4fd3ec6dc999fe6880281739b2f78473953303da2d6e160181c4ddccfa1166c", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_signature.py", "plugin_data": null, "size": 6779, "suppressed": [], "version_id": "1.8.0"}