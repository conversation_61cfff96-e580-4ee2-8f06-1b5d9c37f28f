{"data_mtime": 1753508935, "dep_lines": [12, 13, 14, 15, 5, 11, 1, 3, 4, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._generate_schema", "pydantic._internal._namespace_utils", "collections.abc", "pydantic.config", "__future__", "functools", "inspect", "typing", "pydantic_core", "builtins", "abc", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.plugin", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "re", "types"], "hash": "3df7559d2ce15ceac436d683a03c373c558f5580f95bf80d60f7bca77520e8b8", "id": "pydantic._internal._validate_call", "ignore_all": true, "interface_hash": "a80aee586c904db567a2a34c5c928d70b414b65fcf6d599dfd63acac9f9e7acb", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_validate_call.py", "plugin_data": null, "size": 5321, "suppressed": [], "version_id": "1.8.0"}