{".class": "MypyFile", "_fullname": "pydantic._internal._generics", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "ChainMap": {".class": "SymbolTableNode", "cross_ref": "collections.ChainMap", "kind": "Gdef"}, "ContextVar": {".class": "SymbolTableNode", "cross_ref": "contextvars.ContextVar", "kind": "Gdef"}, "DeepChainMap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.DeepChainMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.DeepChainMap", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.ChainMap"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generics.DeepChainMap", "name": "DeepChainMap", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.DeepChainMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.DeepChainMap", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generics.DeepChainMap", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._generics", "mro": ["pydantic._internal._generics.DeepChainMap", "collections.ChainMap", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.DeepChainMap.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.DeepChainMap", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.DeepChainMap", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._generics.DeepChainMap"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["KT", "VT"], "typeddict_type": null}}, "DictValues": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generics.DictValues", "name": "Dict<PERSON><PERSON><PERSON>", "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "GenericTypesCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generics.GenericTypesCache", "line": 59, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generics.GenericTypesCacheKey"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}], "type_ref": "weakref.<PERSON>ak<PERSON>alueDictionary"}}}, "GenericTypesCacheKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generics.GenericTypesCacheKey", "line": 30, "no_args": false, "normalized": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "KT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "name": "KT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "LimitedDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generics.LimitedDict", "name": "LimitedDict", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generics.LimitedDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._generics", "mro": ["pydantic._internal._generics.LimitedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size_limit"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.LimitedDict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "size_limit"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._generics.LimitedDict"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LimitedDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.LimitedDict.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._generics.LimitedDict"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of LimitedDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "size_limit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generics.LimitedDict.size_limit", "name": "size_limit", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.LimitedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.KT", "id": 1, "name": "KT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "id": 2, "name": "VT", "namespace": "pydantic._internal._generics.LimitedDict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic._internal._generics.LimitedDict"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["KT", "VT"], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "PydanticGenericMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generics.PydanticGenericMetadata", "name": "PydanticGenericMetadata", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generics.PydanticGenericMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generics", "mro": ["pydantic._internal._generics.PydanticGenericMetadata", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["origin", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "NoneType"}]}], ["args", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], ["parameters", {".class": "Instance", "args": ["typing.TypeVar"], "type_ref": "builtins.tuple"}]], "required_keys": ["args", "origin", "parameters"]}}}, "PydanticRecursiveRef": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._forward_ref.PydanticRecursiveRef", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "VT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generics.VT", "name": "VT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "WeakValueDictionary": {".class": "SymbolTableNode", "cross_ref": "weakref.<PERSON>ak<PERSON>alueDictionary", "kind": "Gdef"}, "_GENERIC_TYPES_CACHE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generics._GENERIC_TYPES_CACHE", "name": "_GENERIC_TYPES_CACHE", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generics.GenericTypesCache"}, {".class": "NoneType"}]}], "type_ref": "contextvars.ContextVar"}}}, "_LIMITED_DICT_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._generics._LIMITED_DICT_SIZE", "name": "_LIMITED_DICT_SIZE", "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generics.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generics.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generics.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generics.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generics.__package__", "name": "__package__", "type": "builtins.str"}}, "_early_cache_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "typevar_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics._early_cache_key", "name": "_early_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "typevar_values"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_early_cache_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generics.GenericTypesCacheKey"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_generic_recursion_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generics._generic_recursion_cache", "name": "_generic_recursion_cache", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, {".class": "NoneType"}]}], "type_ref": "contextvars.ContextVar"}}}, "_get_caller_frame_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["depth"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics._get_caller_frame_info", "name": "_get_caller_frame_info", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["depth"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_caller_frame_info", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_late_cache_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["origin", "args", "typevar_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics._late_cache_key", "name": "_late_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["origin", "args", "typevar_values"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_late_cache_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generics.GenericTypesCacheKey"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_typing_extra": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra", "kind": "Gdef"}, "_union_orderings_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["typevar_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics._union_orderings_key", "name": "_union_orderings_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["typevar_values"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_union_orderings_key", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "all_identical": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.all_identical", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "create_generic_submodel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["model_name", "origin", "args", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.create_generic_submodel", "name": "create_generic_submodel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["model_name", "origin", "args", "params"], "arg_types": ["builtins.str", {".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_generic_submodel", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generic_recursion_self_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["origin", "args"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._generics.generic_recursion_self_type", "name": "generic_recursion_self_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["origin", "args"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generic_recursion_self_type", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["pydantic._internal._forward_ref.PydanticRecursiveRef", {".class": "NoneType"}]}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic._internal._generics.generic_recursion_self_type", "name": "generic_recursion_self_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["origin", "args"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generic_recursion_self_type", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["pydantic._internal._forward_ref.PydanticRecursiveRef", {".class": "NoneType"}]}], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.get_args", "name": "get_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_args", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_cached_generic_type_early": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["parent", "typevar_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.get_cached_generic_type_early", "name": "get_cached_generic_type_early", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["parent", "typevar_values"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cached_generic_type_early", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_cached_generic_type_late": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["parent", "typevar_values", "origin", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.get_cached_generic_type_late", "name": "get_cached_generic_type_late", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["parent", "typevar_values", "origin", "args"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cached_generic_type_late", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_model_typevars_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.get_model_typevars_map", "name": "get_model_typevars_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_model_typevars_map", "ret_type": {".class": "Instance", "args": ["typing.TypeVar", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_origin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.get_origin", "name": "get_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_standard_typevars_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.get_standard_typevars_map", "name": "get_standard_typevars_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_standard_typevars_map", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["typing.TypeVar", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_type_ref": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.get_type_ref", "kind": "Gdef"}, "is_model_class": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.is_model_class", "kind": "Gdef"}, "is_union_origin": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.introspection.is_union_origin", "kind": "Gdef"}, "iter_contained_typevars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.iter_contained_typevars", "name": "iter_contained_typevars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_contained_typevars", "ret_type": {".class": "Instance", "args": ["typing.TypeVar"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "map_generic_model_arguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.map_generic_model_arguments", "name": "map_generic_model_arguments", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "args"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map_generic_model_arguments", "ret_type": {".class": "Instance", "args": ["typing.TypeVar", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "prepare_class": {".class": "SymbolTableNode", "cross_ref": "types.prepare_class", "kind": "Gdef"}, "recursively_defined_type_refs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.recursively_defined_type_refs", "name": "recursively_defined_type_refs", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recursively_defined_type_refs", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "replace_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["type_", "type_map"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.replace_types", "name": "replace_types", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["type_", "type_map"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["typing.TypeVar", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_types", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_cached_generic_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["parent", "typevar_values", "type_", "origin", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generics.set_cached_generic_type", "name": "set_cached_generic_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["parent", "typevar_values", "type_", "origin", "args"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_cached_generic_type", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}, "typing_objects": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.typing_objects", "kind": "Gdef"}, "zip_longest": {".class": "SymbolTableNode", "cross_ref": "itertools.zip_longest", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_generics.py"}