{"data_mtime": 1753508935, "dep_lines": [12, 13, 4, 10, 1, 3, 5, 6, 7, 9, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic._internal._import_utils", "collections.abc", "pydantic_core.core_schema", "__future__", "collections", "copy", "functools", "typing", "pydantic_core", "builtins", "_typeshed", "abc"], "hash": "95800f89485f4a07e963aa87f7124f25310ca28c2fdbb4267323a0433cacf745", "id": "pydantic._internal._known_annotated_metadata", "ignore_all": true, "interface_hash": "34961029c9b53ffa03e10592943b9eb7cfe7898ccfc2a73011036032c8cadd14", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_known_annotated_metadata.py", "plugin_data": null, "size": 16213, "suppressed": [], "version_id": "1.8.0"}