{"data_mtime": 1753508935, "dep_lines": [66, 66, 66, 66, 66, 67, 68, 69, 89, 90, 96, 97, 98, 99, 100, 101, 102, 103, 109, 456, 547, 5, 44, 55, 56, 58, 59, 60, 61, 62, 63, 64, 65, 66, 106, 107, 108, 915, 1916, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 39, 41, 43, 44, 55, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 25, 25, 25, 20, 20, 5, 20, 10, 10, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._decorators", "pydantic._internal._discriminated_union", "pydantic._internal._known_annotated_metadata", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic._internal._core_metadata", "pydantic._internal._core_utils", "pydantic._internal._docs_extraction", "pydantic._internal._fields", "pydantic._internal._forward_ref", "pydantic._internal._generics", "pydantic._internal._import_utils", "pydantic._internal._mock_val_ser", "pydantic._internal._namespace_utils", "pydantic._internal._schema_gather", "pydantic._internal._schema_generation_shared", "pydantic._internal._utils", "pydantic._internal._dataclasses", "pydantic._internal._validators", "pydantic._internal._serializers", "collections.abc", "pydantic_core.core_schema", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.functional_validators", "pydantic.json_schema", "pydantic.version", "pydantic.warnings", "pydantic._internal", "pydantic.fields", "pydantic.main", "pydantic.types", "pydantic.v1", "pydantic.dataclasses", "__future__", "collections", "dataclasses", "datetime", "inspect", "os", "pathlib", "re", "sys", "typing", "warnings", "contextlib", "copy", "decimal", "enum", "fractions", "functools", "ipaddress", "itertools", "operator", "types", "uuid", "zoneinfo", "typing_extensions", "pydantic_core", "typing_inspection", "builtins", "_collections_abc", "_typeshed", "_warnings", "abc", "pydantic._internal._model_construction", "pydantic_core._pydantic_core"], "hash": "2d626c9af35d58387543163859e96c152c35fe749c3f011f25da703191f95789", "id": "pydantic._internal._generate_schema", "ignore_all": true, "interface_hash": "2ad3039d4b665460e6a4ad939090a0715d7545fd024284091ea032c42d8d08c2", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_generate_schema.py", "plugin_data": null, "size": 133821, "suppressed": [], "version_id": "1.8.0"}