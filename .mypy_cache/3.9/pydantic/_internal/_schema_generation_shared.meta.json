{"data_mtime": 1753508935, "dep_lines": [13, 14, 15, 7, 9, 12, 3, 5, 7, 1, 1], "dep_prios": [25, 25, 25, 10, 5, 25, 5, 5, 20, 5, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._generate_schema", "pydantic._internal._namespace_utils", "pydantic_core.core_schema", "pydantic.annotated_handlers", "pydantic.json_schema", "__future__", "typing", "pydantic_core", "builtins", "abc"], "hash": "17fadb41bae4a289a0c6cb24747a4fd23509ed309f7b40400c0124aba089e273", "id": "pydantic._internal._schema_generation_shared", "ignore_all": true, "interface_hash": "f342ba6b459af0b924479d9e5c03bd2a3836c9b994daf091c37ba02c63824881", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_schema_generation_shared.py", "plugin_data": null, "size": 4842, "suppressed": [], "version_id": "1.8.0"}