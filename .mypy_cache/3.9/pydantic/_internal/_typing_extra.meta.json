{"data_mtime": 1753508935, "dep_lines": [20, 5, 15, 16, 18, 3, 5, 6, 7, 8, 9, 10, 13, 15, 30, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 20, 10, 10, 10, 5, 5, 5, 20, 25, 5, 30, 30, 30, 30], "dependencies": ["pydantic._internal._namespace_utils", "collections.abc", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic.version", "__future__", "collections", "re", "sys", "types", "typing", "functools", "typing_extensions", "typing_inspection", "pydantic", "builtins", "_typeshed", "abc", "pydantic._internal._model_construction", "pydantic.main"], "hash": "3cedeed89997dc92a54c5cb4130f798b28c08181e026caaab247afe33a280762", "id": "pydantic._internal._typing_extra", "ignore_all": true, "interface_hash": "099b5e2dc6c47ad3e62d22887afabd1bee7f62307c04ae45f48773b7a3f866bb", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_typing_extra.py", "plugin_data": null, "size": 28216, "suppressed": [], "version_id": "1.8.0"}