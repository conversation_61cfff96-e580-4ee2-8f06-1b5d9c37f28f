{"data_mtime": 1753508935, "dep_lines": [6, 1, 2, 5, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 25, 5, 30, 30, 30, 30], "dependencies": ["pydantic.fields", "functools", "typing", "pydantic", "builtins", "abc", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.main"], "hash": "4d18710f93ae63a094a2c8a805d05c254b34a26ec820e36265d60057bcd0f233", "id": "pydantic._internal._import_utils", "ignore_all": true, "interface_hash": "9b16de0f069e9eb82c5fd819505a8b61e80fdfe225aedaf5f8d588744463d7b7", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_import_utils.py", "plugin_data": null, "size": 402, "suppressed": [], "version_id": "1.8.0"}