{"data_mtime": 1753508935, "dep_lines": [18, 19, 20, 21, 22, 7, 14, 17, 25, 26, 3, 5, 6, 8, 9, 10, 11, 12, 14, 15, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 20, 25, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._internal_dataclass", "pydantic._internal._namespace_utils", "pydantic._internal._typing_extra", "pydantic._internal._utils", "collections.abc", "pydantic_core.core_schema", "pydantic.errors", "pydantic.fields", "pydantic.functional_validators", "__future__", "types", "collections", "dataclasses", "functools", "inspect", "itertools", "typing", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_typeshed", "abc", "pydantic_core._pydantic_core"], "hash": "352ed2290bed0e09ec01ddfb9a3aadc0f875f6d779ec527af4bb1c78ee52cb02", "id": "pydantic._internal._decorators", "ignore_all": true, "interface_hash": "ee55b67b46567987647b034b87f8fc1328550f2abafedc0409ef343c7aed4fa7", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_decorators.py", "plugin_data": null, "size": 32638, "suppressed": [], "version_id": "1.8.0"}