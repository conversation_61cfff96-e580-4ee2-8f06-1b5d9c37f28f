{"data_mtime": 1753508935, "dep_lines": [19, 20, 21, 22, 7, 16, 17, 19, 28, 1, 3, 4, 5, 6, 8, 9, 10, 13, 15, 16, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 20, 25, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal._core_utils", "pydantic._internal._forward_ref", "pydantic._internal._utils", "collections.abc", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic._internal", "pydantic.main", "__future__", "sys", "types", "typing", "collections", "contextlib", "<PERSON><PERSON><PERSON>", "itertools", "weakref", "typing_extensions", "typing_inspection", "builtins", "_collections_abc", "_typeshed", "abc", "pydantic._internal._model_construction"], "hash": "0f5ff4c60aa72fa4c941efdf172692936520fc5fa44ff8d105f2e31c52c222a4", "id": "pydantic._internal._generics", "ignore_all": true, "interface_hash": "3b3108ba96fc972db8b28c28329019f15b1187e53372dff1cfbfb9be0a43870d", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/pydantic/_internal/_generics.py", "plugin_data": null, "size": 23849, "suppressed": [], "version_id": "1.8.0"}