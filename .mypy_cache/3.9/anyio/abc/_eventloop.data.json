{".class": "MypyFile", "_fullname": "anyio.abc._eventloop", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef"}, "AbstractContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib.AbstractContextManager", "kind": "Gdef"}, "AddressFamily": {".class": "SymbolTableNode", "cross_ref": "socket.AddressFamily", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["cancelled_exception_class", 1], ["check_cancelled", 1], ["checkpoint", 1], ["connect_tcp", 1], ["connect_unix", 1], ["create_blocking_portal", 1], ["create_cancel_scope", 1], ["create_capacity_limiter", 1], ["create_event", 1], ["create_lock", 1], ["create_semaphore", 1], ["create_task_group", 1], ["create_tcp_listener", 1], ["create_test_runner", 1], ["create_udp_socket", 1], ["create_unix_listener", 1], ["current_default_thread_limiter", 1], ["current_effective_deadline", 1], ["current_time", 1], ["current_token", 1], ["get_current_task", 1], ["get_running_tasks", 1], ["getaddrinfo", 1], ["getnameinfo", 1], ["open_process", 1], ["open_signal_receiver", 1], ["run", 1], ["run_async_from_thread", 1], ["run_sync_from_thread", 1], ["run_sync_in_worker_thread", 1], ["setup_process_pool_exit_at_shutdown", 1], ["sleep", 1], ["wait_all_tasks_blocked", 1], ["wait_readable", 1], ["wait_writable", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "anyio.abc._eventloop.AsyncBackend", "name": "AsyncBackend", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._eventloop.AsyncBackend", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._eventloop", "mro": ["anyio.abc._eventloop.AsyncBackend", "builtins.object"], "names": {".class": "SymbolTable", "cancel_shielded_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "anyio.abc._eventloop.AsyncBackend.cancel_shielded_checkpoint", "name": "cancel_shielded_checkpoint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel_shielded_checkpoint of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.cancel_shielded_checkpoint", "name": "cancel_shielded_checkpoint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel_shielded_checkpoint of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "cancelled_exception_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.cancelled_exception_class", "name": "cancelled_exception_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancelled_exception_class of AsyncBackend", "ret_type": {".class": "TypeType", "item": "builtins.BaseException"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.cancelled_exception_class", "name": "cancelled_exception_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancelled_exception_class of AsyncBackend", "ret_type": {".class": "TypeType", "item": "builtins.BaseException"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "check_cancelled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.check_cancelled", "name": "check_cancelled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_cancelled of AsyncBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.check_cancelled", "name": "check_cancelled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_cancelled of AsyncBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.checkpoint", "name": "checkpoint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "checkpoint of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.checkpoint", "name": "checkpoint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "checkpoint of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "checkpoint_if_cancelled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "anyio.abc._eventloop.AsyncBackend.checkpoint_if_cancelled", "name": "checkpoint_if_cancelled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "checkpoint_if_cancelled of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.checkpoint_if_cancelled", "name": "checkpoint_if_cancelled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "checkpoint_if_cancelled of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "connect_tcp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "host", "port", "local_address"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "host", "port", "local_address"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "host", "port", "local_address"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "connect_unix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.connect_unix", "name": "connect_unix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_unix of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.UNIXSocketStream"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.connect_unix", "name": "connect_unix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_unix of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.UNIXSocketStream"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_blocking_portal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_blocking_portal", "name": "create_blocking_portal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_blocking_portal of AsyncBackend", "ret_type": "anyio.from_thread.BlockingPortal", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_blocking_portal", "name": "create_blocking_portal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_blocking_portal of AsyncBackend", "ret_type": "anyio.from_thread.BlockingPortal", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_cancel_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 5, 5], "arg_names": ["cls", "deadline", "shield"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_cancel_scope", "name": "create_cancel_scope", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["cls", "deadline", "shield"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_cancel_scope of AsyncBackend", "ret_type": "anyio._core._tasks.CancelScope", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_cancel_scope", "name": "create_cancel_scope", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["cls", "deadline", "shield"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_cancel_scope of AsyncBackend", "ret_type": "anyio._core._tasks.CancelScope", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_capacity_limiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "total_tokens"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_capacity_limiter", "name": "create_capacity_limiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "total_tokens"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_capacity_limiter of AsyncBackend", "ret_type": "anyio._core._synchronization.CapacityLimiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_capacity_limiter", "name": "create_capacity_limiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "total_tokens"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_capacity_limiter of AsyncBackend", "ret_type": "anyio._core._synchronization.CapacityLimiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_event", "name": "create_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_event of AsyncBackend", "ret_type": "anyio._core._synchronization.Event", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_event", "name": "create_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_event of AsyncBackend", "ret_type": "anyio._core._synchronization.Event", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 3], "arg_names": ["cls", "fast_acquire"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_lock", "name": "create_lock", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["cls", "fast_acquire"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_lock of AsyncBackend", "ret_type": "anyio._core._synchronization.Lock", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_lock", "name": "create_lock", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["cls", "fast_acquire"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_lock of AsyncBackend", "ret_type": "anyio._core._synchronization.Lock", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_semaphore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "initial_value", "max_value", "fast_acquire"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_semaphore", "name": "create_semaphore", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "initial_value", "max_value", "fast_acquire"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_semaphore of AsyncBackend", "ret_type": "anyio._core._synchronization.Semaphore", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_semaphore", "name": "create_semaphore", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["cls", "initial_value", "max_value", "fast_acquire"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_semaphore of AsyncBackend", "ret_type": "anyio._core._synchronization.Semaphore", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_task_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_task_group", "name": "create_task_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_task_group of AsyncBackend", "ret_type": "anyio.abc._tasks.TaskGroup", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_task_group", "name": "create_task_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_task_group of AsyncBackend", "ret_type": "anyio.abc._tasks.TaskGroup", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_tcp_listener": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "sock"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_tcp_listener", "name": "create_tcp_listener", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "sock"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_tcp_listener of AsyncBackend", "ret_type": "anyio.abc._sockets.SocketListener", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_tcp_listener", "name": "create_tcp_listener", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "sock"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_tcp_listener of AsyncBackend", "ret_type": "anyio.abc._sockets.SocketListener", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_test_runner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_test_runner", "name": "create_test_runner", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "options"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_test_runner of AsyncBackend", "ret_type": "anyio.abc._testing.TestRunner", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_test_runner", "name": "create_test_runner", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "options"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_test_runner of AsyncBackend", "ret_type": "anyio.abc._testing.TestRunner", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_udp_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "family", "local_address", "remote_address", "reuse_port"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_udp_socket", "name": "create_udp_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "family", "local_address", "remote_address", "reuse_port"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.AddressFamily", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_udp_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["anyio.abc._sockets.UDPSocket", "anyio.abc._sockets.ConnectedUDPSocket"]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_udp_socket", "name": "create_udp_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "family", "local_address", "remote_address", "reuse_port"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.AddressFamily", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_udp_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["anyio.abc._sockets.UDPSocket", "anyio.abc._sockets.ConnectedUDPSocket"]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_unix_datagram_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_class"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_datagram_socket", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_datagram_socket", "name": "create_unix_datagram_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["anyio.abc._sockets.UNIXDatagramSocket", "anyio.abc._sockets.ConnectedUNIXDatagramSocket"]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_datagram_socket", "name": "create_unix_datagram_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["anyio.abc._sockets.UNIXDatagramSocket", "anyio.abc._sockets.ConnectedUNIXDatagramSocket"]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_overload", "is_coroutine", "is_decorated"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_datagram_socket", "name": "create_unix_datagram_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.UNIXDatagramSocket"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_datagram_socket", "name": "create_unix_datagram_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.UNIXDatagramSocket"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_overload", "is_coroutine", "is_decorated"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_datagram_socket", "name": "create_unix_datagram_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.ConnectedUNIXDatagramSocket"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_datagram_socket", "name": "create_unix_datagram_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.ConnectedUNIXDatagramSocket"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.UNIXDatagramSocket"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "raw_socket", "remote_path"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_datagram_socket of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.ConnectedUNIXDatagramSocket"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "create_unix_listener": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "sock"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_listener", "name": "create_unix_listener", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "sock"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_listener of AsyncBackend", "ret_type": "anyio.abc._sockets.SocketListener", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.create_unix_listener", "name": "create_unix_listener", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "sock"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "socket.socket"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unix_listener of AsyncBackend", "ret_type": "anyio.abc._sockets.SocketListener", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "current_default_thread_limiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.current_default_thread_limiter", "name": "current_default_thread_limiter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_default_thread_limiter of AsyncBackend", "ret_type": "anyio._core._synchronization.CapacityLimiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.current_default_thread_limiter", "name": "current_default_thread_limiter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_default_thread_limiter of AsyncBackend", "ret_type": "anyio._core._synchronization.CapacityLimiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "current_effective_deadline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.current_effective_deadline", "name": "current_effective_deadline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_effective_deadline of AsyncBackend", "ret_type": "builtins.float", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.current_effective_deadline", "name": "current_effective_deadline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_effective_deadline of AsyncBackend", "ret_type": "builtins.float", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "current_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.current_time", "name": "current_time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_time of AsyncBackend", "ret_type": "builtins.float", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.current_time", "name": "current_time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_time of AsyncBackend", "ret_type": "builtins.float", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "current_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.current_token", "name": "current_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_token of AsyncBackend", "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.current_token", "name": "current_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_token of AsyncBackend", "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_current_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.get_current_task", "name": "get_current_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_task of AsyncBackend", "ret_type": "anyio._core._testing.TaskInfo", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.get_current_task", "name": "get_current_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_task of AsyncBackend", "ret_type": "anyio._core._testing.TaskInfo", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_running_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.get_running_tasks", "name": "get_running_tasks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_running_tasks of AsyncBackend", "ret_type": {".class": "Instance", "args": ["anyio._core._testing.TaskInfo"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.get_running_tasks", "name": "get_running_tasks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_running_tasks of AsyncBackend", "ret_type": {".class": "Instance", "args": ["anyio._core._testing.TaskInfo"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "getaddrinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "host", "port", "family", "type", "proto", "flags"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "host", "port", "family", "type", "proto", "flags"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "socket.AddressFamily"]}, {".class": "UnionType", "items": ["builtins.int", "socket.SocketKind"]}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getaddrinfo of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["socket.AddressFamily", "socket.SocketKind", "builtins.int", "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "host", "port", "family", "type", "proto", "flags"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "socket.AddressFamily"]}, {".class": "UnionType", "items": ["builtins.int", "socket.SocketKind"]}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getaddrinfo of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["socket.AddressFamily", "socket.SocketKind", "builtins.int", "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "getnameinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "sockaddr", "flags"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.getnameinfo", "name": "getnameinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "sockaddr", "flags"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getnameinfo of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.getnameinfo", "name": "getnameinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "sockaddr", "flags"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getnameinfo of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "open_process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 3, 3, 3, 4], "arg_names": ["cls", "command", "stdin", "stdout", "stderr", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.open_process", "name": "open_process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3, 4], "arg_names": ["cls", "command", "stdin", "stdout", "stderr", "kwargs"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._eventloop.StrOrBytesPath"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._eventloop.StrOrBytesPath"}], "type_ref": "typing.Sequence"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open_process of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._subprocesses.Process"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.open_process", "name": "open_process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3, 4], "arg_names": ["cls", "command", "stdin", "stdout", "stderr", "kwargs"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._eventloop.StrOrBytesPath"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._eventloop.StrOrBytesPath"}], "type_ref": "typing.Sequence"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open_process of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._subprocesses.Process"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "open_signal_receiver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 2], "arg_names": ["cls", "signals"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.open_signal_receiver", "name": "open_signal_receiver", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["cls", "signals"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "signal.Signals"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open_signal_receiver of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["signal.Signals"], "type_ref": "typing.AsyncIterator"}], "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.open_signal_receiver", "name": "open_signal_receiver", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["cls", "signals"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "signal.Signals"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open_signal_receiver of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["signal.Signals"], "type_ref": "typing.AsyncIterator"}], "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "func", "args", "kwargs", "options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "func", "args", "kwargs", "options"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of AsyncBackend", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "func", "args", "kwargs", "options"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of AsyncBackend", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "run_async_from_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "func", "args", "token"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.run_async_from_thread", "name": "run_async_from_thread", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "func", "args", "token"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_async_from_thread of AsyncBackend", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.run_async_from_thread", "name": "run_async_from_thread", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "func", "args", "token"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_async_from_thread of AsyncBackend", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "run_sync_from_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "func", "args", "token"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.run_sync_from_thread", "name": "run_sync_from_thread", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "func", "args", "token"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_sync_from_thread of AsyncBackend", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.run_sync_from_thread", "name": "run_sync_from_thread", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "func", "args", "token"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_sync_from_thread of AsyncBackend", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "run_sync_in_worker_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "func", "args", "abandon_on_cancel", "limiter"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.run_sync_in_worker_thread", "name": "run_sync_in_worker_thread", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "func", "args", "abandon_on_cancel", "limiter"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bool", {".class": "UnionType", "items": ["anyio._core._synchronization.CapacityLimiter", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_sync_in_worker_thread of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.run_sync_in_worker_thread", "name": "run_sync_in_worker_thread", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "func", "args", "abandon_on_cancel", "limiter"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bool", {".class": "UnionType", "items": ["anyio._core._synchronization.CapacityLimiter", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_sync_in_worker_thread of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "setup_process_pool_exit_at_shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "workers"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.setup_process_pool_exit_at_shutdown", "name": "setup_process_pool_exit_at_shutdown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "workers"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "Instance", "args": ["anyio.abc._subprocesses.Process"], "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_process_pool_exit_at_shutdown of AsyncBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.setup_process_pool_exit_at_shutdown", "name": "setup_process_pool_exit_at_shutdown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "workers"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "Instance", "args": ["anyio.abc._subprocesses.Process"], "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_process_pool_exit_at_shutdown of AsyncBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "sleep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "delay"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "delay"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "delay"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "wait_all_tasks_blocked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.wait_all_tasks_blocked", "name": "wait_all_tasks_blocked", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_all_tasks_blocked of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.wait_all_tasks_blocked", "name": "wait_all_tasks_blocked", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_all_tasks_blocked of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "wait_readable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.wait_readable", "name": "wait_readable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": ["_typeshed.HasFileno", "builtins.int"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_readable of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.wait_readable", "name": "wait_readable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": ["_typeshed.HasFileno", "builtins.int"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_readable of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "wait_writable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._eventloop.AsyncBackend.wait_writable", "name": "wait_writable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": ["_typeshed.HasFileno", "builtins.int"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_writable of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.abc._eventloop.AsyncBackend.wait_writable", "name": "wait_writable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, {".class": "UnionType", "items": ["_typeshed.HasFileno", "builtins.int"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_writable of AsyncBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.AsyncBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._eventloop.AsyncBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "BlockingPortal": {".class": "SymbolTableNode", "cross_ref": "anyio.from_thread.BlockingPortal", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CancelScope": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.CancelScope", "kind": "Gdef"}, "CapacityLimiter": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.CapacityLimiter", "kind": "Gdef"}, "ConnectedUDPSocket": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.ConnectedUDPSocket", "kind": "Gdef"}, "ConnectedUNIXDatagramSocket": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.ConnectedUNIXDatagramSocket", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Event", "kind": "Gdef"}, "HasFileno": {".class": "SymbolTableNode", "cross_ref": "_typeshed.HasFileno", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "IPSockAddrType": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.IPSockAddrType", "kind": "Gdef"}, "Lock": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Lock", "kind": "Gdef"}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "os.PathLike", "kind": "Gdef"}, "PosArgsT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarTupleExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.PosArgsT", "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "variance": 0}}, "Process": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._subprocesses.Process", "kind": "Gdef"}, "Semaphore": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Semaphore", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Signals": {".class": "SymbolTableNode", "cross_ref": "signal.Signals", "kind": "Gdef"}, "SocketKind": {".class": "SymbolTableNode", "cross_ref": "socket.SocketKind", "kind": "Gdef"}, "SocketListener": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.SocketListener", "kind": "Gdef"}, "SocketStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.SocketStream", "kind": "Gdef"}, "StrOrBytesPath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._eventloop.StrOrBytesPath", "line": 53, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}, {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "os.PathLike"}]}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "T_Retval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._eventloop.T_Retval", "name": "T_<PERSON><PERSON><PERSON>", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TaskGroup": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._tasks.TaskGroup", "kind": "Gdef"}, "TaskInfo": {".class": "SymbolTableNode", "cross_ref": "anyio._core._testing.TaskInfo", "kind": "Gdef"}, "TestRunner": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._testing.TestRunner", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypeVarTuple": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVarTuple", "kind": "Gdef"}, "UDPSocket": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.UDPSocket", "kind": "Gdef"}, "UNIXDatagramSocket": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.UNIXDatagramSocket", "kind": "Gdef"}, "UNIXSocketStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._sockets.UNIXSocketStream", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Unpack", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._eventloop.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._eventloop.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._eventloop.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._eventloop.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._eventloop.__package__", "name": "__package__", "type": "builtins.str"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket.socket", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/abc/_eventloop.py"}