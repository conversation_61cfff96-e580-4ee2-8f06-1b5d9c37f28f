{"data_mtime": 1753508934, "dep_lines": [3, 4, 5, 17, 33, 34, 36, 40, 47, 48, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["anyio.abc._eventloop", "anyio.abc._resources", "anyio.abc._sockets", "anyio.abc._streams", "anyio.abc._subprocesses", "anyio.abc._tasks", "anyio.abc._testing", "anyio._core._synchronization", "anyio._core._tasks", "anyio.from_thread", "__future__", "builtins", "_collections_abc", "abc", "typing", "typing_extensions"], "hash": "7363906d3092fdf428c2f88c5da9cb3e1f26dbd71cc245e6a43afbbb235960ea", "id": "anyio.abc", "ignore_all": true, "interface_hash": "039b330c74e64757c3edca03de78dc18e6db6b967a5f88b25a604e3f9a854a6b", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/abc/__init__.py", "plugin_data": null, "size": 2652, "suppressed": [], "version_id": "1.8.0"}