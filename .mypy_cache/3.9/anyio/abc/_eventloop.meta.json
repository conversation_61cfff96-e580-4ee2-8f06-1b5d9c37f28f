{"data_mtime": 1753508934, "dep_lines": [33, 34, 35, 37, 47, 48, 49, 6, 36, 1, 3, 4, 5, 7, 8, 9, 10, 11, 23, 31, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 5, 25, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._synchronization", "anyio._core._tasks", "anyio._core._testing", "anyio.abc._sockets", "anyio.abc._subprocesses", "anyio.abc._tasks", "anyio.abc._testing", "collections.abc", "anyio.from_thread", "__future__", "math", "sys", "abc", "contextlib", "os", "signal", "socket", "typing", "typing_extensions", "_typeshed", "builtins", "_socket", "anyio._core", "anyio._core._typedattr", "anyio.abc._resources", "anyio.abc._streams", "enum"], "hash": "5262fc0d90af413831ce6c8165c1a6f645a3f55418f01316b9e2e1e52f3258de", "id": "anyio.abc._eventloop", "ignore_all": true, "interface_hash": "84df775bcdd0dbe54a3d85c7aa8e3a03aa2feb3365ed5acc7a6c90d2fe50fe9e", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/abc/_eventloop.py", "plugin_data": null, "size": 9682, "suppressed": [], "version_id": "1.8.0"}