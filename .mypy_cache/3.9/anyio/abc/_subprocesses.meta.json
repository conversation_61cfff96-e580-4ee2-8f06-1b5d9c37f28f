{"data_mtime": 1753508934, "dep_lines": [6, 7, 1, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["anyio.abc._resources", "anyio.abc._streams", "__future__", "abc", "signal", "builtins", "anyio._core", "anyio._core._typedattr", "enum", "typing"], "hash": "72e9803c94e4b4e42dc3adc8a86d250e9c99aaefe5d44125bd01cc8b02602f4f", "id": "anyio.abc._subprocesses", "ignore_all": true, "interface_hash": "27f98336fcd7aaa3dc591581173d3999b62ff3221ef97cf07dea9780b383b93b", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/abc/_subprocesses.py", "plugin_data": null, "size": 2067, "suppressed": [], "version_id": "1.8.0"}