{".class": "MypyFile", "_fullname": "anyio.abc._streams", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyByteReceiveStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._streams.AnyByteReceiveStream", "line": 183, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "type_ref": "anyio.abc._streams.ObjectReceiveStream"}, "anyio.abc._streams.ByteReceiveStream"]}}}, "AnyByteSendStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._streams.AnyByteSendStream", "line": 185, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "type_ref": "anyio.abc._streams.ObjectSendStream"}, "anyio.abc._streams.ByteSendStream"]}}}, "AnyByteStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._streams.AnyByteStream", "line": 187, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "type_ref": "anyio.abc._streams.ObjectStream"}, "anyio.abc._streams.ByteStream"]}}}, "AnyUnreliableByteReceiveStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._streams.AnyUnreliableByteReceiveStream", "line": 175, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}, "anyio.abc._streams.ByteReceiveStream"]}}}, "AnyUnreliableByteSendStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._streams.AnyUnreliableByteSendStream", "line": 179, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "type_ref": "anyio.abc._streams.UnreliableObjectSendStream"}, "anyio.abc._streams.ByteSendStream"]}}}, "AnyUnreliableByteStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._streams.AnyUnreliableByteStream", "line": 181, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "type_ref": "anyio.abc._streams.UnreliableObjectStream"}, "anyio.abc._streams.ByteStream"]}}}, "AsyncResource": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._resources.AsyncResource", "kind": "Gdef"}, "ByteReceiveStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["receive", 1]], "alt_promote": null, "bases": ["anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.ByteReceiveStream", "name": "ByteReceiveStream", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.ByteReceiveStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.ByteReceiveStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.abc._streams.ByteReceiveStream.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._streams.ByteReceiveStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of ByteReceiveStream", "ret_type": "anyio.abc._streams.ByteReceiveStream", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.abc._streams.ByteReceiveStream.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._streams.ByteReceiveStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__anext__ of ByteReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["self", "max_bytes"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._streams.ByteReceiveStream.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "max_bytes"], "arg_types": ["anyio.abc._streams.ByteReceiveStream", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of ByteReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._streams.ByteReceiveStream.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "max_bytes"], "arg_types": ["anyio.abc._streams.ByteReceiveStream", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of ByteReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.ByteReceiveStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._streams.ByteReceiveStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ByteSendStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["send", 1]], "alt_promote": null, "bases": ["anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.ByteSendStream", "name": "ByteSendStream", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.ByteSendStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.ByteSendStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._streams.ByteSendStream.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["anyio.abc._streams.ByteSendStream", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of ByteSendStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._streams.ByteSendStream.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["anyio.abc._streams.ByteSendStream", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of ByteSendStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.ByteSendStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._streams.ByteSendStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ByteStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["receive", 1], ["send", 1], ["send_eof", 1]], "alt_promote": null, "bases": ["anyio.abc._streams.ByteReceiveStream", "anyio.abc._streams.ByteSendStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.ByteStream", "name": "ByteStream", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.ByteStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.ByteStream", "anyio.abc._streams.ByteReceiveStream", "anyio.abc._streams.ByteSendStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "send_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._streams.ByteStream.send_eof", "name": "send_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._streams.ByteStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_eof of ByteStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._streams.ByteStream.send_eof", "name": "send_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._streams.ByteStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_eof of ByteStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.ByteStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._streams.ByteStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "EndOfStream": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.EndOfStream", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Listener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["serve", 1]], "alt_promote": null, "bases": ["anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.Listener", "name": "Listener", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.Listener", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.Listener", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.Listener", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "serve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "task_group"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._streams.Listener.serve", "name": "serve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "task_group"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.Listener", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.Listener"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.Listener", "upper_bound": "builtins.object", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["anyio.abc._tasks.TaskGroup", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serve of Listener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._streams.Listener.serve", "name": "serve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "task_group"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.Listener", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.Listener"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.Listener", "upper_bound": "builtins.object", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["anyio.abc._tasks.TaskGroup", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serve of Listener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.Listener.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.Listener", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.Listener"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_co"], "typeddict_type": null}}, "ObjectReceiveStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["receive", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.ObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.ObjectReceiveStream", "name": "ObjectReceiveStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.ObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.ObjectReceiveStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.ObjectReceiveStream", "anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.ObjectReceiveStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.ObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.ObjectReceiveStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_co"], "typeddict_type": null}}, "ObjectSendStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["send", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.ObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.abc._streams.UnreliableObjectSendStream"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.ObjectSendStream", "name": "ObjectSendStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.ObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.ObjectSendStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.ObjectSendStream", "anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.ObjectSendStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.ObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.abc._streams.ObjectSendStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_contra"], "typeddict_type": null}}, "ObjectStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["receive", 1], ["send", 1], ["send_eof", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.ObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.ObjectReceiveStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.ObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.ObjectSendStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.ObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.UnreliableObjectStream"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.ObjectStream", "name": "ObjectStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.ObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.ObjectStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.ObjectStream", "anyio.abc._streams.ObjectReceiveStream", "anyio.abc._streams.ObjectSendStream", "anyio.abc._streams.UnreliableObjectStream", "anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "send_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._streams.ObjectStream.send_eof", "name": "send_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.ObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.ObjectStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_eof of ObjectStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._streams.ObjectStream.send_eof", "name": "send_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.ObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.ObjectStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_eof of ObjectStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.ObjectStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.ObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.ObjectStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_Item"], "typeddict_type": null}}, "T_Item": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "name": "T_Item", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "name": "T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "T_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "name": "T_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "TaskGroup": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._tasks.TaskGroup", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedAttributeProvider": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.TypedAttributeProvider", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnreliableObjectReceiveStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["receive", 1]], "alt_promote": null, "bases": ["anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.UnreliableObjectReceiveStream", "name": "UnreliableObjectReceiveStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.UnreliableObjectReceiveStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.abc._streams.UnreliableObjectReceiveStream.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of UnreliableObjectReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.abc._streams.UnreliableObjectReceiveStream.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__anext__ of UnreliableObjectReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._streams.UnreliableObjectReceiveStream.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of UnreliableObjectReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._streams.UnreliableObjectReceiveStream.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of UnreliableObjectReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.UnreliableObjectReceiveStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_co", "id": 1, "name": "T_co", "namespace": "anyio.abc._streams.UnreliableObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_co"], "typeddict_type": null}}, "UnreliableObjectSendStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["send", 1]], "alt_promote": null, "bases": ["anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.UnreliableObjectSendStream", "name": "UnreliableObjectSendStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.UnreliableObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.UnreliableObjectSendStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "anyio.abc._streams.UnreliableObjectSendStream.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.UnreliableObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.abc._streams.UnreliableObjectSendStream"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.UnreliableObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of UnreliableObjectSendStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._streams.UnreliableObjectSendStream.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.UnreliableObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.abc._streams.UnreliableObjectSendStream"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.UnreliableObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of UnreliableObjectSendStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.UnreliableObjectSendStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.abc._streams.UnreliableObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.abc._streams.UnreliableObjectSendStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_contra"], "typeddict_type": null}}, "UnreliableObjectStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["aclose", 1], ["receive", 1], ["send", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.UnreliableObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.UnreliableObjectReceiveStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.UnreliableObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.UnreliableObjectSendStream"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._streams.UnreliableObjectStream", "name": "UnreliableObjectStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.UnreliableObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "anyio.abc._streams.UnreliableObjectStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._streams", "mro": ["anyio.abc._streams.UnreliableObjectStream", "anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.UnreliableObjectStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.abc._streams.UnreliableObjectStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.abc._streams.UnreliableObjectStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_Item"], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._streams.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._streams.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._streams.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._streams.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._streams.__package__", "name": "__package__", "type": "builtins.str"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/abc/_streams.py"}