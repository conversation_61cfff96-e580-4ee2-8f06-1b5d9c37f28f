{"data_mtime": 1753508934, "dep_lines": [7, 8, 9, 10, 4, 1, 3, 5, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["anyio._core._exceptions", "anyio._core._typedattr", "anyio.abc._resources", "anyio.abc._tasks", "collections.abc", "__future__", "abc", "typing", "builtins", "anyio._core"], "hash": "1defc9a64016da0e6f78ecdc52ad17b110b69c877f8b7e4bf9df1cb3b523d594", "id": "anyio.abc._streams", "ignore_all": true, "interface_hash": "37671f58c3bbcfef21f7626901c986ea7269d0148c67d4daeb2ae9844f36fbdb", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/abc/_streams.py", "plugin_data": null, "size": 6598, "suppressed": [], "version_id": "1.8.0"}