{"data_mtime": 1753508934, "dep_lines": [15, 5, 1, 3, 4, 6, 7, 12, 1, 1, 1], "dep_prios": [25, 5, 5, 10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["anyio._core._tasks", "collections.abc", "__future__", "sys", "abc", "types", "typing", "typing_extensions", "builtins", "_typeshed", "anyio._core"], "hash": "c8959b330a30bea8e5017e2827797d22cd70fb3c329ebda55f5674d80589aac6", "id": "anyio.abc._tasks", "ignore_all": true, "interface_hash": "5c2eda3cf43a5265079e34f2ad4cb8b90e0cee657a51edd9a2ffc5ec66842a02", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/abc/_tasks.py", "plugin_data": null, "size": 3080, "suppressed": [], "version_id": "1.8.0"}