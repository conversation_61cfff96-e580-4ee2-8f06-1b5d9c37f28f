{".class": "MypyFile", "_fullname": "anyio.to_thread", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CapacityLimiter": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.CapacityLimiter", "kind": "Gdef"}, "PosArgsT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarTupleExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.to_thread.PosArgsT", "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "variance": 0}}, "T_Retval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.to_thread.T_Retval", "name": "T_<PERSON><PERSON><PERSON>", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypeVarTuple": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVarTuple", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Unpack", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.to_thread.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.to_thread.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.to_thread.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.to_thread.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.to_thread.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "current_default_thread_limiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.to_thread.current_default_thread_limiter", "name": "current_default_thread_limiter", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_default_thread_limiter", "ret_type": "anyio._core._synchronization.CapacityLimiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_async_backend": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.get_async_backend", "kind": "Gdef"}, "run_sync": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5], "arg_names": ["func", "args", "abandon_on_cancel", "cancellable", "limiter"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.to_thread.run_sync", "name": "run_sync", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5], "arg_names": ["func", "args", "abandon_on_cancel", "cancellable", "limiter"], "arg_types": [{".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.to_thread.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.to_thread.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.to_thread.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["anyio._core._synchronization.CapacityLimiter", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_sync", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.to_thread.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.to_thread.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.to_thread.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/to_thread.py"}