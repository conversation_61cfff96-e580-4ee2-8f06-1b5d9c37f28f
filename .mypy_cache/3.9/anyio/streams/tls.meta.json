{"data_mtime": 1753508934, "dep_lines": [19, 7, 12, 20, 1, 3, 4, 5, 6, 8, 9, 10, 12, 25, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._typedattr", "collections.abc", "anyio.to_thread", "anyio.abc", "__future__", "logging", "re", "ssl", "sys", "dataclasses", "functools", "typing", "anyio", "typing_extensions", "builtins", "_typeshed", "abc", "anyio._core", "anyio.abc._resources", "anyio.abc._streams", "anyio.abc._tasks"], "hash": "1f1ce9566520a3c49448805ab2cfe5bde7f5a40235b914ab9f2b0cde2106ce5e", "id": "anyio.streams.tls", "ignore_all": true, "interface_hash": "1245d0fdd529df2af0d2e1d267d613cf0919e73f9fd4d2e6a90cf5d6ee9fb1a0", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/streams/tls.py", "plugin_data": null, "size": 13199, "suppressed": [], "version_id": "1.8.0"}