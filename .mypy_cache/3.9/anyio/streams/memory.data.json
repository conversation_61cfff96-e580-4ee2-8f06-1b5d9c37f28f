{".class": "MypyFile", "_fullname": "anyio.streams.memory", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BrokenResourceError": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.BrokenResourceError", "kind": "Gdef"}, "ClosedResourceError": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.ClosedResourceError", "kind": "Gdef"}, "EndOfStream": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.EndOfStream", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Event", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "MemoryObjectItemReceiver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.streams.memory.MemoryObjectItemReceiver", "name": "MemoryObjectItemReceiver", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectItemReceiver", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "anyio.streams.memory.MemoryObjectItemReceiver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "task_info", "type": "anyio._core._testing.TaskInfo"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "item", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectItemReceiver", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "anyio.streams.memory", "mro": ["anyio.streams.memory.MemoryObjectItemReceiver", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectItemReceiver.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectItemReceiver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectItemReceiver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectItemReceiver"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemoryObjectItemReceiver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["task_info", "item"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "anyio.streams.memory.MemoryObjectItemReceiver.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["task_info", "item"], "arg_types": ["anyio._core._testing.TaskInfo", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectItemReceiver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryObjectItemReceiver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectItemReceiver.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["task_info", "item"], "arg_types": ["anyio._core._testing.TaskInfo", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectItemReceiver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryObjectItemReceiver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectItemReceiver.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectItemReceiver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectItemReceiver"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of MemoryObjectItemReceiver", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "item": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectItemReceiver.item", "name": "item", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectItemReceiver", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "task_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectItemReceiver.task_info", "name": "task_info", "type": "anyio._core._testing.TaskInfo"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectItemReceiver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectItemReceiver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectItemReceiver"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_Item"], "typeddict_type": null}}, "MemoryObjectReceiveStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.abc._streams.ObjectReceiveStream"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.streams.memory.MemoryObjectReceiveStream", "name": "MemoryObjectReceiveStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "_state", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "_closed", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "anyio.streams.memory", "mro": ["anyio.streams.memory.MemoryObjectReceiveStream", "anyio.abc._streams.ObjectReceiveStream", "anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of MemoryObjectReceiveStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of MemoryObjectReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of MemoryObjectReceiveStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_state"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemoryObjectReceiveStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of MemoryObjectReceiveStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["_state", "_closed"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["_state", "_closed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryObjectReceiveStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["_state", "_closed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryObjectReceiveStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__post_init__ of MemoryObjectReceiveStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream._closed", "name": "_closed", "type": "builtins.bool"}}, "_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream._state", "name": "_state", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of MemoryObjectReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone of MemoryObjectReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of MemoryObjectReceiveStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of MemoryObjectReceiveStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "receive_nowait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.receive_nowait", "name": "receive_nowait", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive_nowait of MemoryObjectReceiveStream", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.statistics", "name": "statistics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "statistics of MemoryObjectReceiveStream", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "anyio.streams.memory.MemoryObjectStreamStatistics"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectReceiveStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "id": 1, "name": "T_co", "namespace": "anyio.streams.memory.MemoryObjectReceiveStream", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_co"], "typeddict_type": null}}, "MemoryObjectSendStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.abc._streams.ObjectSendStream"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.streams.memory.MemoryObjectSendStream", "name": "MemoryObjectSendStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 192, "name": "_state", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 193, "name": "_closed", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "anyio.streams.memory", "mro": ["anyio.streams.memory.MemoryObjectSendStream", "anyio.abc._streams.ObjectSendStream", "anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of MemoryObjectSendStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_state"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["_state", "_closed"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["_state", "_closed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["_state", "_closed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__post_init__ of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectSendStream._closed", "name": "_closed", "type": "builtins.bool"}}, "_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectSendStream._state", "name": "_state", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.memory.MemoryObjectSendStream.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of MemoryObjectSendStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone of MemoryObjectSendStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.memory.MemoryObjectSendStream.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of MemoryObjectSendStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "send_nowait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.send_nowait", "name": "send_nowait", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_nowait of MemoryObjectSendStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectSendStream.statistics", "name": "statistics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "statistics of MemoryObjectSendStream", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "anyio.streams.memory.MemoryObjectStreamStatistics"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectSendStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "id": 1, "name": "T_contra", "namespace": "anyio.streams.memory.MemoryObjectSendStream", "upper_bound": "builtins.object", "values": [], "variance": 2}], "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_contra"], "typeddict_type": null}}, "MemoryObjectStreamState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.streams.memory.MemoryObjectStreamState", "name": "MemoryObjectStreamState", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "anyio.streams.memory.MemoryObjectStreamState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "max_buffer_size", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "buffer", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.deque"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "open_send_channels", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "open_receive_channels", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "waiting_receivers", "type": {".class": "Instance", "args": ["anyio._core._synchronization.Event", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectItemReceiver"}], "type_ref": "collections.OrderedDict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "waiting_senders", "type": {".class": "Instance", "args": ["anyio._core._synchronization.Event", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.OrderedDict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "anyio.streams.memory", "mro": ["anyio.streams.memory.MemoryObjectStreamState", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_buffer_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectStreamState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_buffer_size"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}, "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemoryObjectStreamState", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["max_buffer_size", "buffer", "open_send_channels", "open_receive_channels", "waiting_receivers", "waiting_senders"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["max_buffer_size", "buffer", "open_send_channels", "open_receive_channels", "waiting_receivers", "waiting_senders"], "arg_types": ["builtins.float", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.deque"}, "builtins.int", "builtins.int", {".class": "Instance", "args": ["anyio._core._synchronization.Event", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectItemReceiver"}], "type_ref": "collections.OrderedDict"}, {".class": "Instance", "args": ["anyio._core._synchronization.Event", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.OrderedDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryObjectStreamState", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["max_buffer_size", "buffer", "open_send_channels", "open_receive_channels", "waiting_receivers", "waiting_senders"], "arg_types": ["builtins.float", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.deque"}, "builtins.int", "builtins.int", {".class": "Instance", "args": ["anyio._core._synchronization.Event", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectItemReceiver"}], "type_ref": "collections.OrderedDict"}, {".class": "Instance", "args": ["anyio._core._synchronization.Event", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.OrderedDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryObjectStreamState", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.buffer", "name": "buffer", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.deque"}}}, "max_buffer_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.max_buffer_size", "name": "max_buffer_size", "type": "builtins.float"}}, "open_receive_channels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.open_receive_channels", "name": "open_receive_channels", "type": "builtins.int"}}, "open_send_channels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.open_send_channels", "name": "open_send_channels", "type": "builtins.int"}}, "statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectStreamState.statistics", "name": "statistics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "statistics of MemoryObjectStreamState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "anyio.streams.memory.MemoryObjectStreamStatistics"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "waiting_receivers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.waiting_receivers", "name": "waiting_receivers", "type": {".class": "Instance", "args": ["anyio._core._synchronization.Event", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectItemReceiver"}], "type_ref": "collections.OrderedDict"}}}, "waiting_senders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.memory.MemoryObjectStreamState.waiting_senders", "name": "waiting_senders", "type": {".class": "Instance", "args": ["anyio._core._synchronization.Event", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "collections.OrderedDict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio.streams.memory.MemoryObjectStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "anyio.streams.memory.MemoryObjectStreamState"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_Item"], "typeddict_type": null}}, "MemoryObjectStreamStatistics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics", "name": "MemoryObjectStreamStatistics", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["current_buffer_used", "max_buffer_size", "open_send_streams", "open_receive_streams", "tasks_waiting_send", "tasks_waiting_receive"]}}, "module_name": "anyio.streams.memory", "mro": ["anyio.streams.memory.MemoryObjectStreamStatistics", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "current_buffer_used", "max_buffer_size", "open_send_streams", "open_receive_streams", "tasks_waiting_send", "tasks_waiting_receive"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "current_buffer_used", "max_buffer_size", "open_send_streams", "open_receive_streams", "tasks_waiting_send", "tasks_waiting_receive"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of MemoryObjectStreamStatistics", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of MemoryObjectStreamStatistics", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of MemoryObjectStreamStatistics", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of MemoryObjectStreamStatistics", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "current_buffer_used", "max_buffer_size", "open_send_streams", "open_receive_streams", "tasks_waiting_send", "tasks_waiting_receive"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "current_buffer_used", "max_buffer_size", "open_send_streams", "open_receive_streams", "tasks_waiting_send", "tasks_waiting_receive"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of MemoryObjectStreamStatistics", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics._source", "name": "_source", "type": "builtins.str"}}, "current_buffer_used": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.current_buffer_used", "name": "current_buffer_used", "type": "builtins.int"}}, "current_buffer_used-redefinition": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectStreamStatistics.current_buffer_used", "kind": "<PERSON><PERSON><PERSON>"}, "max_buffer_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.max_buffer_size", "name": "max_buffer_size", "type": "builtins.float"}}, "max_buffer_size-redefinition": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectStreamStatistics.max_buffer_size", "kind": "<PERSON><PERSON><PERSON>"}, "open_receive_streams": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.open_receive_streams", "name": "open_receive_streams", "type": "builtins.int"}}, "open_receive_streams-redefinition": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectStreamStatistics.open_receive_streams", "kind": "<PERSON><PERSON><PERSON>"}, "open_send_streams": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.open_send_streams", "name": "open_send_streams", "type": "builtins.int"}}, "open_send_streams-redefinition": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectStreamStatistics.open_send_streams", "kind": "<PERSON><PERSON><PERSON>"}, "tasks_waiting_receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.tasks_waiting_receive", "name": "tasks_waiting_receive", "type": "builtins.int"}}, "tasks_waiting_receive-redefinition": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectStreamStatistics.tasks_waiting_receive", "kind": "<PERSON><PERSON><PERSON>"}, "tasks_waiting_send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.tasks_waiting_send", "name": "tasks_waiting_send", "type": "builtins.int"}}, "tasks_waiting_send-redefinition": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectStreamStatistics.tasks_waiting_send", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.MemoryObjectStreamStatistics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "anyio.streams.memory.MemoryObjectStreamStatistics"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.float"], "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "ObjectReceiveStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.ObjectReceiveStream", "kind": "Gdef"}, "ObjectSendStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.ObjectSendStream", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "T_Item": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_Item", "name": "T_Item", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_co", "name": "T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "T_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.memory.T_contra", "name": "T_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "TaskInfo": {".class": "SymbolTableNode", "cross_ref": "anyio._core._testing.TaskInfo", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "WouldBlock": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.WouldBlock", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.memory.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.memory.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.memory.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.memory.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.memory.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "checkpoint": {".class": "SymbolTableNode", "cross_ref": "anyio.lowlevel.checkpoint", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "get_current_task": {".class": "SymbolTableNode", "cross_ref": "anyio._core._testing.get_current_task", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/streams/memory.py"}