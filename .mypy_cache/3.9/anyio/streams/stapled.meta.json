{"data_mtime": 1753508934, "dep_lines": [3, 7, 1, 4, 5, 125, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "anyio.abc", "__future__", "dataclasses", "typing", "anyio", "builtins", "abc", "anyio._core", "anyio._core._typedattr", "anyio.abc._resources", "anyio.abc._streams", "anyio.abc._tasks"], "hash": "534f690ab98ec3d9243617bab4aa29b26d5020c4f59454c5bed6fe03b4887b89", "id": "anyio.streams.stapled", "ignore_all": true, "interface_hash": "99c356ff10d21c684da82d37c1a1f92da39b6438b82b601a75c44a05c0b2483e", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/streams/stapled.py", "plugin_data": null, "size": 4302, "suppressed": [], "version_id": "1.8.0"}