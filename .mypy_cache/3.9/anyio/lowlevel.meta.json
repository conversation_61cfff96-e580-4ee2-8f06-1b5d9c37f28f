{"data_mtime": 1753508934, "dep_lines": [8, 1, 3, 4, 5, 6, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 30], "dependencies": ["anyio._core._eventloop", "__future__", "enum", "dataclasses", "typing", "weakref", "builtins", "abc"], "hash": "9e48265bef92771195a747262d461ace392282f7919b91d8efe816f01a69f686", "id": "anyio.lowlevel", "ignore_all": true, "interface_hash": "387047f23065e0594296bf891314af7cf99397e36f37fdc0448ef7b7c1899001", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/lowlevel.py", "plugin_data": null, "size": 4169, "suppressed": [], "version_id": "1.8.0"}