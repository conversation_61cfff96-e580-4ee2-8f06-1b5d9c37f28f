{".class": "MypyFile", "_fullname": "anyio._core._subprocesses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIterable": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterable", "kind": "Gdef"}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "io.BytesIO", "kind": "Gdef"}, "CalledProcessError": {".class": "SymbolTableNode", "cross_ref": "subprocess.CalledProcessError", "kind": "Gdef"}, "CompletedProcess": {".class": "SymbolTableNode", "cross_ref": "subprocess.CompletedProcess", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "PIPE": {".class": "SymbolTableNode", "cross_ref": "subprocess.PIPE", "kind": "Gdef"}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "os.PathLike", "kind": "Gdef"}, "Process": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._subprocesses.Process", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "StrOrBytesPath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio._core._subprocesses.StrOrBytesPath", "line": 19, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": ["builtins.str"], "type_ref": "os.PathLike"}, {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "os.PathLike"}]}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._subprocesses.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._subprocesses.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._subprocesses.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._subprocesses.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._subprocesses.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "create_task_group": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.create_task_group", "kind": "Gdef"}, "get_async_backend": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.get_async_backend", "kind": "Gdef"}, "open_process": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["command", "stdin", "stdout", "stderr", "cwd", "env", "startupinfo", "creationflags", "start_new_session", "pass_fds", "user", "group", "extra_groups", "umask"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio._core._subprocesses.open_process", "name": "open_process", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["command", "stdin", "stdout", "stderr", "cwd", "env", "startupinfo", "creationflags", "start_new_session", "pass_fds", "user", "group", "extra_groups", "umask"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._subprocesses.StrOrBytesPath"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._subprocesses.StrOrBytesPath"}], "type_ref": "typing.Sequence"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._subprocesses.StrOrBytesPath"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", "builtins.bool", {".class": "Instance", "args": ["builtins.int"], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"]}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open_process", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._subprocesses.Process"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "run_process": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["command", "input", "stdin", "stdout", "stderr", "check", "cwd", "env", "startupinfo", "creationflags", "start_new_session", "pass_fds", "user", "group", "extra_groups", "umask"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio._core._subprocesses.run_process", "name": "run_process", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["command", "input", "stdin", "stdout", "stderr", "check", "cwd", "env", "startupinfo", "creationflags", "start_new_session", "pass_fds", "user", "group", "extra_groups", "umask"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._subprocesses.StrOrBytesPath"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._subprocesses.StrOrBytesPath"}], "type_ref": "typing.Sequence"}]}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio._core._subprocesses.StrOrBytesPath"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", "builtins.bool", {".class": "Instance", "args": ["builtins.int"], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"]}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_process", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "subprocess.CompletedProcess"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/_core/_subprocesses.py"}