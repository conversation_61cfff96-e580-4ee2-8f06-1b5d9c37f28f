{"data_mtime": 1753508934, "dep_lines": [6, 19, 1, 3, 4, 5, 7, 8, 9, 11, 16, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 10, 10, 5, 5, 5, 10, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "anyio.abc", "__future__", "math", "sys", "threading", "contextlib", "importlib", "typing", "sniffio", "typing_extensions", "builtins", "_typeshed", "abc", "anyio.abc._eventloop"], "hash": "b7fb40c0114f8c5f23ad91a3949e9b6d8cba280ddb8ec6d9c55f66be1f6dd62d", "id": "anyio._core._eventloop", "ignore_all": true, "interface_hash": "2153175c66652094ce3786432fa9ea4cc2a71a14efaa924ccb073a50207c9ab1", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/_core/_eventloop.py", "plugin_data": null, "size": 4695, "suppressed": [], "version_id": "1.8.0"}