{"data_mtime": 1753508934, "dep_lines": [18, 6, 17, 19, 1, 3, 4, 5, 7, 8, 9, 17, 22, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 10, 10, 5, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._fileio", "collections.abc", "anyio.to_thread", "anyio.lowlevel", "__future__", "os", "sys", "tempfile", "io", "types", "typing", "anyio", "_typeshed", "builtins", "abc", "anyio._core._synchronization", "anyio.abc", "anyio.abc._resources", "typing_extensions"], "hash": "b3efee71a7176f1047e41a397a8eb994dd253f065077907cc8d37ff67011a423", "id": "anyio._core._tempfile", "ignore_all": true, "interface_hash": "f393c60273418befa481ad246a1ef4b70429c33f3993f3113239891d77b98f67", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/_core/_tempfile.py", "plugin_data": null, "size": 19696, "suppressed": [], "version_id": "1.8.0"}