{".class": "MypyFile", "_fullname": "anyio._core._eventloop", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncBackend": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._eventloop.AsyncBackend", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "BACKENDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "anyio._core._eventloop.BACKENDS", "name": "BACKENDS", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "PosArgsT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarTupleExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._eventloop.PosArgsT", "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "T_Retval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._eventloop.T_Retval", "name": "T_<PERSON><PERSON><PERSON>", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypeVarTuple": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVarTuple", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Unpack", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._eventloop.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._eventloop.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._eventloop.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._eventloop.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._eventloop.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "claim_worker_thread": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["backend_class", "token"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "anyio._core._eventloop.claim_worker_thread", "name": "claim_worker_thread", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["backend_class", "token"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "claim_worker_thread", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._eventloop.claim_worker_thread", "name": "claim_worker_thread", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["backend_class", "token"], "arg_types": [{".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "builtins.object"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "claim_worker_thread", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "current_time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio._core._eventloop.current_time", "name": "current_time", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_time", "ret_type": "builtins.float", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_all_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio._core._eventloop.get_all_backends", "name": "get_all_backends", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all_backends", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_async_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["asynclib_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio._core._eventloop.get_async_backend", "name": "get_async_backend", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["asynclib_name"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_async_backend", "ret_type": {".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_cancelled_exc_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio._core._eventloop.get_cancelled_exc_class", "name": "get_cancelled_exc_class", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cancelled_exc_class", "ret_type": {".class": "TypeType", "item": "builtins.BaseException"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "import_module": {".class": "SymbolTableNode", "cross_ref": "importlib.import_module", "kind": "Gdef"}, "loaded_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "anyio._core._eventloop.loaded_backends", "name": "loaded_backends", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "anyio.abc._eventloop.AsyncBackend"}], "type_ref": "builtins.dict"}}}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "run": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["func", "args", "backend", "backend_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio._core._eventloop.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["func", "args", "backend", "backend_options"], "arg_types": [{".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._eventloop.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._eventloop.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "sleep": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["delay"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio._core._eventloop.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["delay"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sleep_forever": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio._core._eventloop.sleep_forever", "name": "sleep_forever", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep_forever", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sleep_until": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["deadline"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio._core._eventloop.sleep_until", "name": "sleep_until", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["deadline"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep_until", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sniffio": {".class": "SymbolTableNode", "cross_ref": "sniffio", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "threadlocals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "anyio._core._eventloop.threadlocals", "name": "threadlocals", "type": "threading.local"}}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/_core/_eventloop.py"}