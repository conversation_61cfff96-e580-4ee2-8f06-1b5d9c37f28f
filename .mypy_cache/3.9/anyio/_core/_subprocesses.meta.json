{"data_mtime": 1753508934, "dep_lines": [11, 12, 4, 10, 1, 3, 5, 6, 7, 8, 17, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["anyio._core._eventloop", "anyio._core._tasks", "collections.abc", "anyio.abc", "__future__", "sys", "io", "os", "subprocess", "typing", "typing_extensions", "builtins", "_typeshed", "abc", "anyio.abc._resources", "anyio.abc._subprocesses"], "hash": "1179b98a02fb763e7989890f95b61502ab5bab12718d4fba3a77524c8c7d4918", "id": "anyio._core._subprocesses", "ignore_all": true, "interface_hash": "523458ffe55ae7cc2b9c7917b40193211604313fdbf992b7378c897e22302693", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/_core/_subprocesses.py", "plugin_data": null, "size": 8047, "suppressed": [], "version_id": "1.8.0"}