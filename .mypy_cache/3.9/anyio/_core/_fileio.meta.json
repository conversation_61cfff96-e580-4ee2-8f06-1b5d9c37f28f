{"data_mtime": 1753508934, "dep_lines": [6, 27, 28, 1, 3, 4, 5, 13, 14, 16, 27, 31, 33, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 10, 10, 5, 5, 5, 20, 25, 25, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "anyio.to_thread", "anyio.abc", "__future__", "os", "pathlib", "sys", "dataclasses", "functools", "typing", "anyio", "types", "_typeshed", "builtins", "abc", "anyio._core._synchronization", "anyio.abc._resources", "typing_extensions"], "hash": "a8566190b233d1c19796ebe287fbdc3d44ee720abc505560b130ad61b8a36488", "id": "anyio._core._fileio", "ignore_all": true, "interface_hash": "23c4ddce4368c97f63086e1c672452b92cfc21101aee891f67bedac206db55e3", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/anyio/_core/_fileio.py", "plugin_data": null, "size": 23340, "suppressed": [], "version_id": "1.8.0"}