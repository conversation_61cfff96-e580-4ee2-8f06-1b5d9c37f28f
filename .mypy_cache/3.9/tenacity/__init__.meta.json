{"data_mtime": 1753508937, "dep_lines": [26, 28, 31, 47, 51, 60, 72, 76, 80, 91, 662, 18, 19, 20, 21, 22, 23, 24, 25, 26, 84, 89, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 20, 10, 25, 5, 30, 30, 30, 30], "dependencies": ["concurrent.futures", "tenacity._utils", "tenacity.retry", "tenacity.nap", "tenacity.stop", "tenacity.wait", "tenacity.before", "tenacity.after", "tenacity.before_sleep", "tenacity.asyncio", "tenacity.tornadoweb", "dataclasses", "functools", "sys", "threading", "time", "typing", "warnings", "abc", "concurrent", "tornado", "types", "builtins", "_typeshed", "concurrent.futures._base", "tenacity.asyncio.retry", "typing_extensions"], "hash": "544a9aef72ea9b07749169f27f59c3091be9e09b613fd4705223506667e74d3c", "id": "tenacity", "ignore_all": true, "interface_hash": "0fffe5b516f72f59b0a01db3d9f9933a8aecd028264a0ddd678fe4d6352221f0", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tenacity/__init__.py", "plugin_data": null, "size": 24021, "suppressed": [], "version_id": "1.8.0"}