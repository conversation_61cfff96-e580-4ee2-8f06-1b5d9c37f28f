{"data_mtime": 1753508937, "dep_lines": [23, 26, 15, 16, 18, 23, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["tornado.gen", "tornado.concurrent", "sys", "typing", "tenacity", "tornado", "builtins", "abc", "asyncio", "asyncio.futures", "tenacity.retry", "tenacity.stop", "tenacity.wait"], "hash": "bd2d4e35f3d8a06ccfc756ac41a55b7e8a3a0fdb4f2334b2b098a99bad58ab0f", "id": "tenacity.tornadoweb", "ignore_all": true, "interface_hash": "84197730826c529061b5b9180f04dfb2f7c0b7dea242b23d91e9f6698aa7075e", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tenacity/tornadoweb.py", "plugin_data": null, "size": 2125, "suppressed": [], "version_id": "1.8.0"}