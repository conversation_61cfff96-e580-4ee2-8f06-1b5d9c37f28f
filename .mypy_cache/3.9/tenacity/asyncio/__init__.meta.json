{"data_mtime": 1753508937, "dep_lines": [34, 31, 39, 42, 43, 18, 19, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 25, 25, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tenacity.asyncio.retry", "tenacity._utils", "tenacity.retry", "tenacity.stop", "tenacity.wait", "functools", "sys", "typing", "tenacity", "builtins", "_typeshed", "abc", "concurrent", "concurrent.futures", "concurrent.futures._base", "tenacity.after", "tenacity.before"], "hash": "3e80c6cc737e9de4ebd86800d538b739132b4a65081146e56e24a2bb2c4a1d32", "id": "tenacity.asyncio", "ignore_all": true, "interface_hash": "fe160b970c5741b5633ede1871fff3034abf9d397aa5a5993fe92d2cdb75d1a7", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tenacity/asyncio/__init__.py", "plugin_data": null, "size": 7773, "suppressed": [], "version_id": "1.8.0"}