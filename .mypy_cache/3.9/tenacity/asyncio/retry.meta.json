{"data_mtime": 1753508937, "dep_lines": [19, 16, 17, 19, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 30], "dependencies": ["tenacity._utils", "abc", "typing", "tenacity", "builtins", "tenacity.retry"], "hash": "ca6bbc17525f01eadce6f0ced15e3eda40af1d7ab271eda3a4ed4d96de9a2b12", "id": "tenacity.asyncio.retry", "ignore_all": true, "interface_hash": "29ef0f199278f1bf13a9bba66792504babd6ccf4053f7a759eec7e88eac9cf8e", "mtime": 1753467206, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/tenacity/asyncio/retry.py", "plugin_data": null, "size": 4244, "suppressed": [], "version_id": "1.8.0"}