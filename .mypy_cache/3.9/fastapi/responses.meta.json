{"data_mtime": 1753508936, "dep_lines": [3, 1, 1, 1, 1, 12, 18], "dep_prios": [5, 5, 5, 30, 30, 10, 10], "dependencies": ["starlette.responses", "typing", "builtins", "abc", "starlette"], "hash": "40d410970a4a850a083d94d35a4a5cf5dfd019e199fda5503da0d5de743c9bb7", "id": "fastapi.responses", "ignore_all": true, "interface_hash": "fefbc29aadc464224445e80dfef5d863ec886bdd656321fec187645b37b84c6e", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/responses.py", "plugin_data": null, "size": 1761, "suppressed": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "version_id": "1.8.0"}