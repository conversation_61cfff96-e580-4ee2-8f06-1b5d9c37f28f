{"data_mtime": 1753508935, "dep_lines": [1, 2, 3, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["types", "enum", "typing", "pydantic", "builtins", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "9c56f7eac2b70d2a2aa32a3b322c32de678a2b951d1418240202d2cd09545722", "id": "fastapi.types", "ignore_all": true, "interface_hash": "f5ac904c4cab4227c005f5c1c7ce50679174567e0b40fb57062508b342d35eba", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/types.py", "plugin_data": null, "size": 383, "suppressed": [], "version_id": "1.8.0"}