{"data_mtime": 1753508936, "dep_lines": [4, 12, 1, 2, 13, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 18], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["fastapi._compat", "fastapi.logger", "enum", "typing", "pydantic", "typing_extensions", "builtins", "abc", "annotated_types", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.fields", "pydantic.main", "pydantic.networks", "pydantic.types", "re"], "hash": "3ea931422a9c1208caba17d42163d93d07324dcb9bb54081def70e6cbb01ed51", "id": "fastapi.openapi.models", "ignore_all": true, "interface_hash": "5a229499df70daed1e19d68f370808c1b8592c2dbcbf13bfa8c415610c51f878", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/openapi/models.py", "plugin_data": null, "size": 15397, "suppressed": ["email_validator"], "version_id": "1.8.0"}