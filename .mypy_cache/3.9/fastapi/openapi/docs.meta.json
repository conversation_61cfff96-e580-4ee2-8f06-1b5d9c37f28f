{"data_mtime": 1753508936, "dep_lines": [4, 5, 1, 2, 6, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["fastapi.encoders", "starlette.responses", "json", "typing", "typing_extensions", "builtins", "abc", "starlette"], "hash": "cd20efe3163a5c770ab1a1b8cf29351ea4a7ad9b5f645041d09ed9064e581cf1", "id": "fastapi.openapi.docs", "ignore_all": true, "interface_hash": "a56813a809e887e14d94c8787c4948bcfbc4c1cf8638514f371718074b168685", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/openapi/docs.py", "plugin_data": null, "size": 10345, "suppressed": [], "version_id": "1.8.0"}