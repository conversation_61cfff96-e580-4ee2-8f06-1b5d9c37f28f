{"data_mtime": 1753508936, "dep_lines": [20, 22, 23, 24, 27, 1, 2, 3, 4, 5, 6, 14, 15, 16, 17, 18, 21, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.types", "pydantic.color", "pydantic.networks", "pydantic.types", "fastapi._compat", "dataclasses", "datetime", "collections", "decimal", "enum", "ipaddress", "pathlib", "re", "types", "typing", "uuid", "pydantic", "typing_extensions", "builtins", "_decimal", "_typeshed", "abc", "os", "pydantic._internal", "pydantic._internal._repr", "pydantic.v1", "pydantic.v1.json", "pydantic_core", "pydantic_core._pydantic_core"], "hash": "2efc1898578ecf8b55c2f801a02e6bbd99dbafb859afbdca1ab53c3bbcd2a6d5", "id": "fastapi.encoders", "ignore_all": true, "interface_hash": "5ffcada74d9c9f822c9527ffea32fad2d0479b03a811d2ad2c2a43722c652729", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/encoders.py", "plugin_data": null, "size": 11068, "suppressed": [], "version_id": "1.8.0"}