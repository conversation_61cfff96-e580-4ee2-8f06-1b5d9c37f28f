{"data_mtime": 1753508937, "dep_lines": [3, 4, 5, 6, 7, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.openapi.models", "fastapi.security.base", "starlette.exceptions", "starlette.requests", "starlette.status", "typing", "typing_extensions", "builtins", "abc", "enum", "fastapi.openapi", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette"], "hash": "701239678cd65632f5b89aec8d378bcbb31a7c73c03b61d03f34eba72a086160", "id": "fastapi.security.api_key", "ignore_all": true, "interface_hash": "dc7e7f82577e8266a8fc86a2b216dfb263afe1cbc34b4dc975ec0d263b9cdc39", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/security/api_key.py", "plugin_data": null, "size": 9094, "suppressed": [], "version_id": "1.8.0"}