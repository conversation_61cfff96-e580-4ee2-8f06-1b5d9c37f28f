{"data_mtime": 1753508938, "dep_lines": [1, 4, 9, 15, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30], "dependencies": ["fastapi.security.api_key", "fastapi.security.http", "fastapi.security.oauth2", "fastapi.security.open_id_connect_url", "builtins", "abc", "typing"], "hash": "6cef29366c6a5515d48df97698e2a25592e7d05a4143ad555185639a9a676c9c", "id": "fastapi.security", "ignore_all": true, "interface_hash": "94f6d742c43e4170c64c73246da7e9cdf71b8bb2cc42678afcb9713e73ac2968", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/security/__init__.py", "plugin_data": null, "size": 881, "suppressed": [], "version_id": "1.8.0"}