{".class": "MypyFile", "_fullname": "fastapi.responses", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "FileResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.FileResponse", "kind": "Gdef"}, "HTMLResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.HTMLResponse", "kind": "Gdef"}, "JSONResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.JSONResponse", "kind": "Gdef"}, "ORJSONResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.JSONResponse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.responses.ORJSONResponse", "name": "ORJSONResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "fastapi.responses.ORJSONResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.responses", "mro": ["fastapi.responses.ORJSONResponse", "starlette.responses.JSONResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.responses.ORJSONResponse.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["fastapi.responses.ORJSONResponse", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of ORJSONResponse", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.responses.ORJSONResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.responses.ORJSONResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PlainTextResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.PlainTextResponse", "kind": "Gdef"}, "RedirectResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.RedirectResponse", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.Response", "kind": "Gdef"}, "StreamingResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.StreamingResponse", "kind": "Gdef"}, "UJSONResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.JSONResponse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.responses.UJSONResponse", "name": "UJSONResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "fastapi.responses.UJSONResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.responses", "mro": ["fastapi.responses.UJSONResponse", "starlette.responses.JSONResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.responses.UJSONResponse.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["fastapi.responses.UJSONResponse", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of UJSONResponse", "ret_type": "builtins.bytes", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.responses.UJSONResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.responses.UJSONResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.responses.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.responses.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.responses.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.responses.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.responses.__package__", "name": "__package__", "type": "builtins.str"}}, "orjson": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi.responses.orjson", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "fastapi.responses.orjson", "source_any": null, "type_of_any": 3}}}, "ujson": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi.responses.ujson", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "fastapi.responses.ujson", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/responses.py"}