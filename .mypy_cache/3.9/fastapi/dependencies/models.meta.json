{"data_mtime": 1753508937, "dep_lines": [5, 4, 1, 2, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["fastapi.security.base", "fastapi._compat", "dataclasses", "typing", "builtins", "_typeshed", "abc", "fastapi.security"], "hash": "3e397abf1fb89d9e53b5af6425adfe45f40a917b42a52d3df8584c82cf5e58db", "id": "fastapi.dependencies.models", "ignore_all": true, "interface_hash": "bbfa73391305c00fa0cd024196e60f6534bf31912b26c36d2cd4cc52ea4f8c2c", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/dependencies/models.py", "plugin_data": null, "size": 1507, "suppressed": [], "version_id": "1.8.0"}