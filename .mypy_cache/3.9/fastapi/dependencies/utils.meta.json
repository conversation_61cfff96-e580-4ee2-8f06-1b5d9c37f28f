{"data_mtime": 1753508937, "dep_lines": [54, 56, 57, 58, 22, 23, 49, 50, 55, 59, 61, 62, 63, 64, 71, 72, 73, 1, 2, 3, 4, 5, 21, 22, 60, 74, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 20, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["fastapi.dependencies.models", "fastapi.security.base", "fastapi.security.oauth2", "fastapi.security.open_id_connect_url", "fastapi.params", "fastapi._compat", "fastapi.background", "fastapi.concurrency", "fastapi.logger", "fastapi.utils", "pydantic.fields", "starlette.background", "starlette.concurrency", "starlette.datastructures", "starlette.requests", "starlette.responses", "starlette.websockets", "inspect", "contextlib", "copy", "dataclasses", "typing", "anyio", "<PERSON><PERSON><PERSON>", "pydantic", "typing_extensions", "builtins", "abc", "starlette", "tenacity", "tenacity._utils"], "hash": "c0637e0406f43691bef3d9c0fce9654b4178c18c067e11e06fc22e4f7313a9c9", "id": "fastapi.dependencies.utils", "ignore_all": true, "interface_hash": "30716afe5c21b54ec92d13b0c11dce4066a7d8812d79862db2d1b459f11ec4c5", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/dependencies/utils.py", "plugin_data": null, "size": 36619, "suppressed": [], "version_id": "1.8.0"}