{"data_mtime": 1753508937, "dep_lines": [35, 36, 3, 25, 26, 34, 45, 46, 52, 53, 61, 62, 63, 64, 65, 75, 76, 1, 2, 3, 4, 5, 6, 7, 8, 25, 60, 61, 77, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 20, 10, 10, 5, 5, 5, 20, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.dependencies.models", "fastapi.dependencies.utils", "email.message", "fastapi.params", "fastapi._compat", "fastapi.datastructures", "fastapi.encoders", "fastapi.exceptions", "fastapi.types", "fastapi.utils", "starlette.routing", "starlette.concurrency", "starlette.exceptions", "starlette.requests", "starlette.responses", "starlette.types", "starlette.websockets", "asyncio", "dataclasses", "email", "inspect", "json", "contextlib", "enum", "typing", "<PERSON><PERSON><PERSON>", "pydantic", "starlette", "typing_extensions", "builtins", "_collections_abc", "_typeshed", "abc", "fastapi.dependencies", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic._internal._utils", "pydantic.fields", "pydantic.main", "pydantic_core", "pydantic_core._pydantic_core", "re", "starlette.background", "starlette.convertors", "starlette.middleware"], "hash": "f9268e82a6ac78ac399a54c293e165892e96c7995afc28dd57916a48f0e65bd8", "id": "fastapi.routing", "ignore_all": true, "interface_hash": "9b9a625ee4b43b797b5fc63aeaa8d7f5bf6e942b9a95582cc1ac3e3b83f43f98", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/routing.py", "plugin_data": null, "size": 176337, "suppressed": [], "version_id": "1.8.0"}