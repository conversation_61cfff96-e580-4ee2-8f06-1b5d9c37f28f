{"data_mtime": **********, "dep_lines": [18, 28, 30, 34, 1, 2, 3, 4, 15, 17, 29, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 10, 10, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi._compat", "fastapi.datastructures", "pydantic.fields", "fastapi.routing", "re", "warnings", "dataclasses", "typing", "weakref", "<PERSON><PERSON><PERSON>", "pydantic", "typing_extensions", "builtins", "abc", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.main", "pydantic_core", "pydantic_core._pydantic_core", "starlette", "starlette.routing"], "hash": "cbc063e6db4c688f6d4b80fad0e5205ea2a792d06bf7d35d6149c71d5f4b8286", "id": "fastapi.utils", "ignore_all": true, "interface_hash": "cd4636c0c1d076eefb76bf9f412f9529f621c9cef58ebbcb4ab2a178f7cbb34a", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/utils.py", "plugin_data": null, "size": 7948, "suppressed": [], "version_id": "1.8.0"}