{"data_mtime": 1753508936, "dep_lines": [55, 58, 59, 296, 22, 23, 25, 26, 60, 61, 72, 302, 305, 308, 332, 341, 344, 1, 2, 3, 4, 5, 6, 24, 27, 63, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._schema_generation_shared", "pydantic._internal._typing_extra", "pydantic._internal._utils", "fastapi.openapi.constants", "fastapi.exceptions", "fastapi.types", "pydantic.version", "starlette.datastructures", "pydantic.fields", "pydantic.json_schema", "pydantic_core.core_schema", "pydantic.class_validators", "pydantic.error_wrappers", "pydantic.errors", "pydantic.schema", "pydantic.typing", "pydantic.utils", "collections", "copy", "dataclasses", "enum", "functools", "typing", "pydantic", "typing_extensions", "pydantic_core", "builtins", "_collections_abc", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.annotated_handlers", "pydantic.deprecated", "pydantic.deprecated.config", "pydantic.main", "pydantic.networks", "pydantic.type_adapter", "pydantic_core._pydantic_core"], "hash": "3f019365de9dfaeda8e9817d33ca506a1b81b43ff7ab72a98fbbd4e7e9e00a17", "id": "fastapi._compat", "ignore_all": true, "interface_hash": "c0bfe269c13110e85000858fc8f5eaa4bdcf54a30ab6163e29ca31cd39ba891d", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/_compat.py", "plugin_data": null, "size": 24228, "suppressed": [], "version_id": "1.8.0"}