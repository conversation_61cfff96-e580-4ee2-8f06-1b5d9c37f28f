{"data_mtime": 1753508937, "dep_lines": [5, 3, 4, 1, 3, 6, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.openapi.models", "fastapi.params", "fastapi._compat", "typing", "<PERSON><PERSON><PERSON>", "typing_extensions", "builtins", "abc", "fastapi.openapi", "pydantic", "pydantic.networks", "pydantic_core", "pydantic_core._pydantic_core"], "hash": "24734f2c862fa00c1d9d965abc856cc4e6adf31db77d7fca977deb7a1ec72a5f", "id": "fastapi.param_functions", "ignore_all": true, "interface_hash": "2e3e91591fb6e3a1fb69c97d654fef385ba741f0ecb101014dbffa87a68d60a1", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/param_functions.py", "plugin_data": null, "size": 64019, "suppressed": [], "version_id": "1.8.0"}