{".class": "MypyFile", "_fullname": "<PERSON><PERSON><PERSON>", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIRouter": {".class": "SymbolTableNode", "cross_ref": "fastapi.routing.APIRouter", "kind": "Gdef"}, "BackgroundTasks": {".class": "SymbolTableNode", "cross_ref": "fastapi.background.BackgroundTasks", "kind": "Gdef"}, "Body": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Body", "kind": "Gdef"}, "Cookie": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.<PERSON><PERSON>", "kind": "Gdef"}, "Depends": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Depends", "kind": "Gdef"}, "FastAPI": {".class": "SymbolTableNode", "cross_ref": "fastapi.applications.FastAPI", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.File", "kind": "Gdef"}, "Form": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Form", "kind": "Gdef"}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "fastapi.exceptions.HTTPException", "kind": "Gdef"}, "Header": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Header", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Path", "kind": "Gdef"}, "Query": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Query", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.Response", "kind": "Gdef"}, "Security": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Security", "kind": "Gdef"}, "UploadFile": {".class": "SymbolTableNode", "cross_ref": "fastapi.datastructures.UploadFile", "kind": "Gdef"}, "WebSocket": {".class": "SymbolTableNode", "cross_ref": "starlette.websockets.WebSocket", "kind": "Gdef"}, "WebSocketDisconnect": {".class": "SymbolTableNode", "cross_ref": "starlette.websockets.WebSocketDisconnect", "kind": "Gdef"}, "WebSocketException": {".class": "SymbolTableNode", "cross_ref": "fastapi.exceptions.WebSocketException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "fastapi.__version__", "name": "__version__", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "cross_ref": "starlette.status", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/__init__.py"}