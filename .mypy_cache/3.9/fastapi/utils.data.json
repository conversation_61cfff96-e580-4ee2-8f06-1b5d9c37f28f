{".class": "MypyFile", "_fullname": "fastapi.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIRoute": {".class": "SymbolTableNode", "cross_ref": "fastapi.routing.APIRoute", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseConfig": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.BaseConfig", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "DefaultPlaceholder": {".class": "SymbolTableNode", "cross_ref": "fastapi.datastructures.DefaultPlaceholder", "kind": "Gdef"}, "DefaultType": {".class": "SymbolTableNode", "cross_ref": "fastapi.datastructures.DefaultType", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "ModelField": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.ModelField", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PYDANTIC_V2": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.PYDANTIC_V2", "kind": "Gdef"}, "PydanticSchemaGenerationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticSchemaGenerationError", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Undefined": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.Undefined", "kind": "Gdef"}, "UndefinedType": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.UndefinedType", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Validator": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.Validator", "kind": "Gdef"}, "WeakKeyDictionary": {".class": "SymbolTableNode", "cross_ref": "weakref.<PERSON>eyDictionary", "kind": "Gdef"}, "_CLONED_TYPES_CACHE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "fastapi.utils._CLONED_TYPES_CACHE", "name": "_CLONED_TYPES_CACHE", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}], "type_ref": "typing.MutableMapping"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "create_cloned_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["field", "cloned_types"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.utils.create_cloned_field", "name": "create_cloned_field", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["field", "cloned_types"], "arg_types": ["fastapi._compat.ModelField", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}], "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_cloned_field", "ret_type": "fastapi._compat.ModelField", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_model": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.create_model", "kind": "Gdef"}, "create_model_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["name", "type_", "class_validators", "default", "required", "model_config", "field_info", "alias", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.utils.create_model_field", "name": "create_model_field", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["name", "type_", "class_validators", "default", "required", "model_config", "field_info", "alias", "mode"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "fastapi._compat.Validator"}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "pydantic_core._pydantic_core.PydanticUndefinedType"]}, {".class": "TypeType", "item": "fastapi._compat.BaseConfig"}, {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_model_field", "ret_type": "fastapi._compat.ModelField", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "deep_dict_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["main_dict", "update_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.utils.deep_dict_update", "name": "deep_dict_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["main_dict", "update_dict"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deep_dict_update", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fastapi": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "generate_operation_id_for_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3], "arg_names": ["name", "path", "method"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.utils.generate_operation_id_for_path", "name": "generate_operation_id_for_path", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3], "arg_names": ["name", "path", "method"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_operation_id_for_path", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_unique_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["route"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.utils.generate_unique_id", "name": "generate_unique_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["route"], "arg_types": ["fastapi.routing.APIRoute"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_unique_id", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_path_param_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.utils.get_path_param_names", "name": "get_path_param_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_path_param_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_value_or_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["first_item", "extra_items"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.utils.get_value_or_default", "name": "get_value_or_default", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["first_item", "extra_items"], "arg_types": [{".class": "UnionType", "items": ["fastapi.datastructures.DefaultPlaceholder", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.datastructures.DefaultType", "id": -1, "name": "DefaultType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "UnionType", "items": ["fastapi.datastructures.DefaultPlaceholder", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.datastructures.DefaultType", "id": -1, "name": "DefaultType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_value_or_default", "ret_type": {".class": "UnionType", "items": ["fastapi.datastructures.DefaultPlaceholder", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.datastructures.DefaultType", "id": -1, "name": "DefaultType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.datastructures.DefaultType", "id": -1, "name": "DefaultType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "is_body_allowed_for_status_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["status_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.utils.is_body_allowed_for_status_code", "name": "is_body_allowed_for_status_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["status_code"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_body_allowed_for_status_code", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.is_dataclass", "kind": "Gdef"}, "lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.lenient_issubclass", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/utils.py"}