{"data_mtime": 1753508936, "dep_lines": [4, 6, 1, 2, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["anyio.to_thread", "starlette.concurrency", "contextlib", "typing", "anyio", "builtins", "abc", "anyio._core", "anyio._core._synchronization", "starlette", "types"], "hash": "322adfa30a12a6431067c8ff8346716902a957a781dc6f9d0794e07170ab8040", "id": "fastapi.concurrency", "ignore_all": true, "interface_hash": "99a6ea82a7911365219bbadb3a33e4660b48644401386e9c512ba354c690244a", "mtime": 1753488573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/fastapi/concurrency.py", "plugin_data": null, "size": 1424, "suppressed": [], "version_id": "1.8.0"}