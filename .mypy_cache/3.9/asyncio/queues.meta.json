{"data_mtime": 1753508932, "dep_lines": [2, 1, 3, 6, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 30, 30], "dependencies": ["asyncio.events", "sys", "typing", "types", "builtins", "_typeshed", "abc"], "hash": "90b47bf317dd1ffbe90cb436540fe5c90611317c4654569f0c05c85e4840f06a", "id": "asyncio.queues", "ignore_all": true, "interface_hash": "4abe84eeb64d1deea9c565583fe6d7025508159da74f4655650c14ea92fec2c4", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/queues.pyi", "plugin_data": null, "size": 1270, "suppressed": [], "version_id": "1.8.0"}