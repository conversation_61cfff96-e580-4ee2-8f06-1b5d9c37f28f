{"data_mtime": 1753508932, "dep_lines": [2, 1, 2, 3, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 30, 30], "dependencies": ["asyncio.transports", "_typeshed", "asyncio", "typing", "builtins", "abc", "typing_extensions"], "hash": "ede7386cc6e43b45ddd3976c2c5a28382a79accc20f2636ed9e3eebeedf08959", "id": "asyncio.protocols", "ignore_all": true, "interface_hash": "f8e7311cd78c879376654e139534db5f3dc2fba7ed0940a54b8ad8c0c305721c", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/protocols.pyi", "plugin_data": null, "size": 1631, "suppressed": [], "version_id": "1.8.0"}