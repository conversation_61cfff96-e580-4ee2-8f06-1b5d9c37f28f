{"data_mtime": 1753508932, "dep_lines": [4, 8, 8, 8, 9, 1, 2, 3, 5, 6, 8, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 20, 5, 30], "dependencies": ["collections.abc", "asyncio.events", "asyncio.protocols", "asyncio.transports", "asyncio.base_events", "ssl", "sys", "_typeshed", "typing", "typing_extensions", "asyncio", "builtins", "abc"], "hash": "f32847ead8919f44b73f56ad24797f2aca1a0de9437b29415705b1b191507cd3", "id": "asyncio.streams", "ignore_all": true, "interface_hash": "07bcd6d10b5eaa3877ca11b05a40a00a6984c915349c25820d1762cd8fed2918", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/streams.pyi", "plugin_data": null, "size": 6690, "suppressed": [], "version_id": "1.8.0"}