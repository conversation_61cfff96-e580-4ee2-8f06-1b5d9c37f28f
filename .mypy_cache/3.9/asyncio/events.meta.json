{"data_mtime": 1753508932, "dep_lines": [5, 12, 13, 14, 15, 16, 17, 1, 2, 3, 4, 6, 7, 8, 9, 11, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "asyncio.base_events", "asyncio.futures", "asyncio.protocols", "asyncio.tasks", "asyncio.transports", "asyncio.unix_events", "ssl", "sys", "_typeshed", "abc", "<PERSON><PERSON><PERSON>", "socket", "typing", "typing_extensions", "asyncio", "builtins", "_socket", "enum"], "hash": "5c8709b336cab9e6cd463449288c6591809b30446c32bf51ed64797d8c647925", "id": "asyncio.events", "ignore_all": true, "interface_hash": "0a481822c7ad14836975307dd4705e8ce09d682847e68db2246ed400095ca033", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/events.pyi", "plugin_data": null, "size": 23574, "suppressed": [], "version_id": "1.8.0"}