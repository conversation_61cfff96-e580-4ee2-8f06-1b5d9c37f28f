{"data_mtime": 1753508932, "dep_lines": [3, 2, 7, 1, 4, 5, 14, 17, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["concurrent.futures._base", "collections.abc", "asyncio.events", "sys", "typing", "typing_extensions", "<PERSON><PERSON><PERSON>", "types", "builtins", "_typeshed", "abc", "concurrent", "concurrent.futures"], "hash": "beb19f7fcbc8aef1aad2dd7a69276c55312611872d6da11402dfcefbf4a1fb54", "id": "asyncio.futures", "ignore_all": true, "interface_hash": "43a12115c1af6eeec0f07b9ab70eddb7e0a9127a7d82439dad33ba1887ece295", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/futures.pyi", "plugin_data": null, "size": 2651, "suppressed": [], "version_id": "1.8.0"}