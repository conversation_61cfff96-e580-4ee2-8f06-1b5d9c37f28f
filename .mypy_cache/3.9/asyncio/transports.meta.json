{"data_mtime": 1753508932, "dep_lines": [1, 2, 3, 4, 5, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30], "dependencies": ["asyncio.events", "asyncio.protocols", "collections.abc", "socket", "typing", "builtins", "abc"], "hash": "f242e0158ec82b79cd3ddab8782681eb59952ae8bb8ee144876a38ee4da6a172", "id": "asyncio.transports", "ignore_all": true, "interface_hash": "fae46293683a00029c08d85fd77a339459122c549f31bd0d941fbe4bf2b7156b", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/transports.pyi", "plugin_data": null, "size": 2040, "suppressed": [], "version_id": "1.8.0"}