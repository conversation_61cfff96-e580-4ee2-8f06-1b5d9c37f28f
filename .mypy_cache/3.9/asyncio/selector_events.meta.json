{"data_mtime": 1753508932, "dep_lines": [3, 1, 3, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 30, 30, 30], "dependencies": ["asyncio.base_events", "selectors", "asyncio", "builtins", "abc", "asyncio.events", "typing"], "hash": "57603bd439e8bc0593343071538914941ea6da2e06bcded9cf3bd32ce088b97a", "id": "asyncio.selector_events", "ignore_all": true, "interface_hash": "1b78ff86f82058a18d4f989cd12657a9e40dd818ad9b23a84b1a08d23ef3cd80", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/selector_events.pyi", "plugin_data": null, "size": 223, "suppressed": [], "version_id": "1.8.0"}