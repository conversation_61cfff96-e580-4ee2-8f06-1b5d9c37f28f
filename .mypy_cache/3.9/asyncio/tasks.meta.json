{"data_mtime": 1753508932, "dep_lines": [1, 3, 9, 10, 1, 2, 4, 5, 6, 8, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["concurrent.futures", "collections.abc", "asyncio.events", "asyncio.futures", "concurrent", "sys", "types", "typing", "typing_extensions", "asyncio", "builtins", "_typeshed", "abc", "concurrent.futures._base"], "hash": "d56bd1eb9e3e7d6f5a5344a3d4375ad96d1ea0cbc74f992be8feb42c1b590ca7", "id": "asyncio.tasks", "ignore_all": true, "interface_hash": "59f65a627778e7d2c5df15f09747626d601f6365e8f98059b3795077135c37e4", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/tasks.pyi", "plugin_data": null, "size": 18652, "suppressed": [], "version_id": "1.8.0"}