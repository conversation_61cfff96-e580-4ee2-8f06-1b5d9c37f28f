{"data_mtime": 1753508932, "dep_lines": [4, 8, 9, 1, 2, 3, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "asyncio.events", "asyncio.selector_events", "sys", "types", "abc", "typing", "typing_extensions", "builtins", "_typeshed", "asyncio.base_events", "selectors"], "hash": "db943b225d442670f4f681aa18f55444c15fcb2bcd421d3a7743983509df1c9e", "id": "asyncio.unix_events", "ignore_all": true, "interface_hash": "828c08b9df7c2a165b0b1e04cd8e1a558e01bb057f8b65e2a10889ea44b5cfec", "mtime": 1753508879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/unix_events.pyi", "plugin_data": null, "size": 8664, "suppressed": [], "version_id": "1.8.0"}