{"data_mtime": 1753508936, "dep_lines": [15, 24, 16, 21, 22, 23, 1, 3, 4, 5, 6, 7, 1, 1, 1, 1, 9, 10, 11, 12, 13, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 30, 30, 30, 10, 10, 10, 10, 10, 20], "dependencies": ["httpcore._backends.base", "httpcore._sync.interfaces", "httpcore._exceptions", "httpcore._models", "httpcore._synchronization", "httpcore._trace", "__future__", "enum", "logging", "time", "types", "typing", "builtins", "abc", "httpcore._backends", "typing_extensions"], "hash": "031538ca172aebc067e6fa9d258b625ca6148fb9ef8586f1cf7bf8ad3e319ef0", "id": "httpcore._sync.http2", "ignore_all": true, "interface_hash": "ea0fc16a412ba3d35f722563f30dec0d34b493cb98f2b55037b2d5bcc3b1a679", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_sync/http2.py", "plugin_data": null, "size": 23400, "suppressed": ["h2.config", "h2.connection", "h2.events", "h2.exceptions", "h2.settings", "h2"], "version_id": "1.8.0"}