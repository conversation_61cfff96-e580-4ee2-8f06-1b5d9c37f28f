{".class": "MypyFile", "_fullname": "httpcore._sync", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConnectionInterface": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.interfaces.ConnectionInterface", "kind": "Gdef"}, "ConnectionPool": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.connection_pool.ConnectionPool", "kind": "Gdef"}, "HTTP11Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.http11.HTTP11Connection", "kind": "Gdef"}, "HTTP2Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.http2.HTTP2Connection", "kind": "Gdef"}, "HTTPConnection": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.connection.HTTPConnection", "kind": "Gdef"}, "HTTPProxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.http_proxy.HTTPProxy", "kind": "Gdef"}, "SOCKSProxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.socks_proxy.SOCKSProxy", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._sync.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_sync/__init__.py"}