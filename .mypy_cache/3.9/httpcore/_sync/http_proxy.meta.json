{"data_mtime": 1753508936, "dep_lines": [8, 22, 23, 24, 25, 328, 9, 10, 19, 20, 21, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["httpcore._backends.base", "httpcore._sync.connection", "httpcore._sync.connection_pool", "httpcore._sync.http11", "httpcore._sync.interfaces", "httpcore._sync.http2", "httpcore._exceptions", "httpcore._models", "httpcore._ssl", "httpcore._synchronization", "httpcore._trace", "__future__", "base64", "logging", "ssl", "typing", "builtins", "abc", "httpcore._async", "httpcore._async.http_proxy", "httpcore._backends", "typing_extensions"], "hash": "fda97fe9cacab8466edb0cafbb8f7745922625d0499e3e6818a3632ce24bd99a", "id": "httpcore._sync.http_proxy", "ignore_all": true, "interface_hash": "b8af918fa4be8576716703a60fe69b8562abe6d03b27c499cd107627513e822c", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_sync/http_proxy.py", "plugin_data": null, "size": 14463, "suppressed": [], "version_id": "1.8.0"}