{"data_mtime": 1753508936, "dep_lines": [8, 9, 15, 16, 17, 280, 10, 11, 12, 13, 14, 1, 3, 4, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 10], "dependencies": ["httpcore._backends.sync", "httpcore._backends.base", "httpcore._sync.connection_pool", "httpcore._sync.http11", "httpcore._sync.interfaces", "httpcore._sync.http2", "httpcore._exceptions", "httpcore._models", "httpcore._ssl", "httpcore._synchronization", "httpcore._trace", "__future__", "logging", "ssl", "builtins", "abc", "httpcore._backends", "typing"], "hash": "cde8195bd4a7aa3dbff7dd831496bcfc2a6939506454be0083076e4644ad6a44", "id": "httpcore._sync.socks_proxy", "ignore_all": true, "interface_hash": "728dde7f3558a8a1d220fe645a8164fecf484377a306cdc36c13cbd03c2f7740", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_sync/socks_proxy.py", "plugin_data": null, "size": 13614, "suppressed": ["<PERSON><PERSON>"], "version_id": "1.8.0"}