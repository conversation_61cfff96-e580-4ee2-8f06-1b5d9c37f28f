{"data_mtime": 1753508936, "dep_lines": [8, 9, 13, 14, 131, 144, 10, 11, 12, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["httpcore._backends.sync", "httpcore._backends.base", "httpcore._sync.connection", "httpcore._sync.interfaces", "httpcore._sync.socks_proxy", "httpcore._sync.http_proxy", "httpcore._exceptions", "httpcore._models", "httpcore._synchronization", "__future__", "ssl", "sys", "types", "typing", "builtins", "_typeshed", "abc", "anyio", "anyio._core", "anyio._core._synchronization", "httpcore._backends"], "hash": "6be4fc2d3b14c5ceebd16c356ad7c7483a163e34347c0f1497b4b7f85d0c8fbd", "id": "httpcore._sync.connection_pool", "ignore_all": true, "interface_hash": "93e7d65090a23ca0d8e0d7915a8d843d66ba1e94a3a2066f919dd6124165460f", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py", "plugin_data": null, "size": 16955, "suppressed": [], "version_id": "1.8.0"}