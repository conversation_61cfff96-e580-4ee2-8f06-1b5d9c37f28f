{"data_mtime": 1753508936, "dep_lines": [1, 2, 3, 4, 5, 8, 20, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["httpcore._sync.connection", "httpcore._sync.connection_pool", "httpcore._sync.http11", "httpcore._sync.http_proxy", "httpcore._sync.interfaces", "httpcore._sync.http2", "httpcore._sync.socks_proxy", "builtins", "abc", "typing"], "hash": "2410c8817b7995ad4b089d6c2d078a863285a4b9e936bf12beceb3da78b77e08", "id": "httpcore._sync", "ignore_all": true, "interface_hash": "85096c7b2cc94ebc1332bb3f4f6632b60a2c0e77abbc4bf06cc2fdc39c3f9b1a", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_sync/__init__.py", "plugin_data": null, "size": 1141, "suppressed": [], "version_id": "1.8.0"}