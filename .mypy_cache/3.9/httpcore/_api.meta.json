{"data_mtime": 1753508936, "dep_lines": [7, 6, 1, 3, 4, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 30], "dependencies": ["httpcore._sync.connection_pool", "httpcore._models", "__future__", "contextlib", "typing", "builtins", "abc"], "hash": "ba7666783b1c8415821823c2c244b75a8b7d7ae2ba6e0fe42b12ed193c70db5e", "id": "httpcore._api", "ignore_all": true, "interface_hash": "89ffdebc67e4616bd99f76b36f55657d9ff1cabbcf97dba2fc965d1a12eb0391", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_api.py", "plugin_data": null, "size": 3146, "suppressed": [], "version_id": "1.8.0"}