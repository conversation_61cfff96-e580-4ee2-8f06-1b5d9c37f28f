{"data_mtime": 1753508937, "dep_lines": [11, 18, 19, 51, 64, 1, 2, 20, 37, 38, 39, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["httpcore._backends.base", "httpcore._backends.mock", "httpcore._backends.sync", "httpcore._backends.anyio", "httpcore._backends.trio", "httpcore._api", "httpcore._async", "httpcore._exceptions", "httpcore._models", "httpcore._ssl", "httpcore._sync", "builtins", "abc", "typing", "typing_extensions"], "hash": "f644ff92a0a10822544c7c30db866647f7b371d6e94585a4b03fa060dce464ff", "id": "httpcore", "ignore_all": true, "interface_hash": "e2a8711b652930b3d54b6edb83d7a3884e108c89f9db6998ad68fdf9cc26b1df", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/__init__.py", "plugin_data": null, "size": 3445, "suppressed": [], "version_id": "1.8.0"}