{"data_mtime": 1753508936, "dep_lines": [8, 9, 13, 14, 131, 144, 10, 11, 12, 1, 3, 4, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30], "dependencies": ["httpcore._backends.auto", "httpcore._backends.base", "httpcore._async.connection", "httpcore._async.interfaces", "httpcore._async.socks_proxy", "httpcore._async.http_proxy", "httpcore._exceptions", "httpcore._models", "httpcore._synchronization", "__future__", "ssl", "sys", "types", "typing", "builtins", "_typeshed", "abc", "httpcore._backends"], "hash": "0ce210dacd9909ff6a7f0c61ccca6b4cf2ea08bf0ec465e2285eaa447c6f5726", "id": "httpcore._async.connection_pool", "ignore_all": true, "interface_hash": "12473a71862930796f77cb51fa0a9539f47745d409553a0af4d243704b4b0e3d", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_async/connection_pool.py", "plugin_data": null, "size": 17307, "suppressed": [], "version_id": "1.8.0"}