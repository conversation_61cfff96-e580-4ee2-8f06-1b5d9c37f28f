{".class": "MypyFile", "_fullname": "httpcore._backends.auto", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncNetworkBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.AsyncNetworkBackend", "kind": "Gdef"}, "AsyncNetworkStream": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.AsyncNetworkStream", "kind": "Gdef"}, "AutoBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._backends.base.AsyncNetworkBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._backends.auto.AutoBackend", "name": "AutoBackend", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpcore._backends.auto.AutoBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._backends.auto", "mro": ["httpcore._backends.auto.AutoBackend", "httpcore._backends.base.AsyncNetworkBackend", "builtins.object"], "names": {".class": "SymbolTable", "_backend": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpcore._backends.auto.AutoBackend._backend", "name": "_backend", "type": "httpcore._backends.base.AsyncNetworkBackend"}}, "_init_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpcore._backends.auto.AutoBackend._init_backend", "name": "_init_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._backends.auto.AutoBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_backend of AutoBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connect_tcp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "host", "port", "timeout", "local_address", "socket_options"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpcore._backends.auto.AutoBackend.connect_tcp", "name": "connect_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "host", "port", "timeout", "local_address", "socket_options"], "arg_types": ["httpcore._backends.auto.AutoBackend", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._backends.base.SOCKET_OPTION"}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_tcp of AutoBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "httpcore._backends.base.AsyncNetworkStream"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connect_unix_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "timeout", "socket_options"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpcore._backends.auto.AutoBackend.connect_unix_socket", "name": "connect_unix_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "timeout", "socket_options"], "arg_types": ["httpcore._backends.auto.AutoBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._backends.base.SOCKET_OPTION"}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_unix_socket of AutoBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "httpcore._backends.base.AsyncNetworkStream"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sleep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "seconds"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpcore._backends.auto.AutoBackend.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "seconds"], "arg_types": ["httpcore._backends.auto.AutoBackend", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep of AutoBackend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._backends.auto.AutoBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._backends.auto.AutoBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SOCKET_OPTION": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.SOCKET_OPTION", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._backends.auto.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._backends.auto.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._backends.auto.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._backends.auto.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._backends.auto.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "current_async_library": {".class": "SymbolTableNode", "cross_ref": "httpcore._synchronization.current_async_library", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_backends/auto.py"}