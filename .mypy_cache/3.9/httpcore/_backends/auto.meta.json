{"data_mtime": 1753508936, "dep_lines": [6, 14, 18, 5, 1, 3, 1, 1, 1, 1], "dep_prios": [5, 20, 20, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["httpcore._backends.base", "httpcore._backends.trio", "httpcore._backends.anyio", "httpcore._synchronization", "__future__", "typing", "builtins", "abc", "sniffio", "sniffio._impl"], "hash": "cced77e8f2999ac6930caf87464f38780f8c520f3fdb025fe0dbe62b8df6022a", "id": "httpcore._backends.auto", "ignore_all": true, "interface_hash": "128f7c801be31d207318e810cae15e86b8c26417da13806c7bec33dd74117f31", "mtime": 1753467209, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Documents/KonveyN2AI/venv/lib/python3.13/site-packages/httpcore/_backends/auto.py", "plugin_data": null, "size": 1662, "suppressed": [], "version_id": "1.8.0"}