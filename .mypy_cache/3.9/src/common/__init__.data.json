{".class": "MypyFile", "_fullname": "src.common", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdviceRequest": {".class": "SymbolTableNode", "cross_ref": "src.common.models.AdviceRequest", "kind": "Gdef"}, "AnswerResponse": {".class": "SymbolTableNode", "cross_ref": "src.common.models.AnswerResponse", "kind": "Gdef"}, "JsonRpcError": {".class": "SymbolTableNode", "cross_ref": "src.common.models.JsonRpcError", "kind": "Gdef"}, "JsonRpcErrorCode": {".class": "SymbolTableNode", "cross_ref": "src.common.models.JsonRpcErrorCode", "kind": "Gdef"}, "JsonRpcRequest": {".class": "SymbolTableNode", "cross_ref": "src.common.models.JsonRpcRequest", "kind": "Gdef"}, "JsonRpcResponse": {".class": "SymbolTableNode", "cross_ref": "src.common.models.JsonRpcResponse", "kind": "Gdef"}, "QueryRequest": {".class": "SymbolTableNode", "cross_ref": "src.common.models.QueryRequest", "kind": "Gdef"}, "SearchRequest": {".class": "SymbolTableNode", "cross_ref": "src.common.models.SearchRequest", "kind": "Gdef"}, "Snippet": {".class": "SymbolTableNode", "cross_ref": "src.common.models.Snippet", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.common.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.common.__version__", "name": "__version__", "type": "builtins.str"}}}, "path": "src/common/__init__.py"}