{"data_mtime": 1753508938, "dep_lines": [8, 6, 1, 2, 3, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.common.models", "tenacity.retry", "uuid", "contextlib", "typing", "httpx", "tenacity", "builtins", "abc", "datetime", "http", "http.cookiejar", "httpx._auth", "httpx._client", "httpx._config", "httpx._exceptions", "httpx._models", "httpx._transports", "httpx._transports.base", "httpx._urls", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "ssl", "tenacity.asyncio", "tenacity.asyncio.retry", "tenacity.stop", "tenacity.wait", "typing_extensions"], "hash": "eaf282d19e59c4bd2ca51cbc5c3b008097eedf46b5025eda4b7e8b49a25e2fba", "id": "src.common.rpc_client", "ignore_all": false, "interface_hash": "f4cc2af9b5d248fb911fb990172afc1a92e4a41ae172dc4204ba0742fdd5e95b", "mtime": 1753508901, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src/common/rpc_client.py", "plugin_data": null, "size": 1910, "suppressed": [], "version_id": "1.8.0"}