{"data_mtime": 1753508936, "dep_lines": [13, 1, 1, 1], "dep_prios": [5, 5, 30, 30], "dependencies": ["src.common.models", "builtins", "abc", "typing"], "hash": "f123f7e136b941f04769e8deef6a52331282a14f0dc92f47dada0e631765328d", "id": "src.common", "ignore_all": false, "interface_hash": "2e3950691ef0517af1c7d3f72e721b2da789e0d06cebbf7f81077f58142fdf11", "mtime": 1753505848, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src/common/__init__.py", "plugin_data": null, "size": 758, "suppressed": [], "version_id": "1.8.0"}