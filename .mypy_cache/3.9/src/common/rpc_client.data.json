{".class": "MypyFile", "_fullname": "src.common.rpc_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "JsonRpcClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.common.rpc_client.JsonRpcClient", "name": "JsonRpcClient", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.common.rpc_client.JsonRpcClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.common.rpc_client", "mro": ["src.common.rpc_client.JsonRpcClient", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "url", "timeout", "max_retries"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.common.rpc_client.JsonRpcClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "url", "timeout", "max_retries"], "arg_types": ["src.common.rpc_client.JsonRpcClient", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of JsonRpcClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "method", "params", "id"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "src.common.rpc_client.JsonRpcClient.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "method", "params", "id"], "arg_types": ["src.common.rpc_client.JsonRpcClient", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of JsonRpcClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.common.models.JsonRpcResponse"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.common.rpc_client.JsonRpcClient.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "method", "params", "id"], "arg_types": ["src.common.rpc_client.JsonRpcClient", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of JsonRpcClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.common.models.JsonRpcResponse"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "max_retries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.common.rpc_client.JsonRpcClient.max_retries", "name": "max_retries", "type": "builtins.int"}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_decorated"], "fullname": "src.common.rpc_client.JsonRpcClient.session", "name": "session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.common.rpc_client.JsonRpcClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session of JsonRpcClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.common.rpc_client.JsonRpcClient.session", "name": "session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.common.rpc_client.JsonRpcClient"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session of JsonRpcClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "type_ref": "contextlib._AsyncGeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.common.rpc_client.JsonRpcClient.timeout", "name": "timeout", "type": "builtins.int"}}, "url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.common.rpc_client.JsonRpcClient.url", "name": "url", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.common.rpc_client.JsonRpcClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.common.rpc_client.JsonRpcClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JsonRpcError": {".class": "SymbolTableNode", "cross_ref": "src.common.models.JsonRpcError", "kind": "Gdef"}, "JsonRpcRequest": {".class": "SymbolTableNode", "cross_ref": "src.common.models.JsonRpcRequest", "kind": "Gdef"}, "JsonRpcResponse": {".class": "SymbolTableNode", "cross_ref": "src.common.models.JsonRpcResponse", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.rpc_client.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.rpc_client.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.rpc_client.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.rpc_client.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.common.rpc_client.__package__", "name": "__package__", "type": "builtins.str"}}, "asynccontextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.asynccontextmanager", "kind": "Gdef"}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef"}, "retry": {".class": "SymbolTableNode", "cross_ref": "tenacity.retry", "kind": "Gdef"}, "stop_after_attempt": {".class": "SymbolTableNode", "cross_ref": "tenacity.stop.stop_after_attempt", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}, "wait_exponential": {".class": "SymbolTableNode", "cross_ref": "tenacity.wait.wait_exponential", "kind": "Gdef"}}, "path": "src/common/rpc_client.py"}