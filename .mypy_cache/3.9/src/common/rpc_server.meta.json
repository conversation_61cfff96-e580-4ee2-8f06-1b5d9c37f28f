{"data_mtime": 1753508938, "dep_lines": [18, 15, 8, 9, 10, 11, 12, 14, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.common.models", "fastapi.responses", "json", "logging", "traceback", "inspect", "typing", "<PERSON><PERSON><PERSON>", "pydantic", "builtins", "abc", "enum", "json.decoder", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_core", "pydantic_core._pydantic_core", "starlette", "starlette.background", "starlette.requests", "starlette.responses", "types", "typing_extensions"], "hash": "33b349d9c8400a6f1be89afa3c5d7c7d030440d7b2ef701f7c28186f0b5c5445", "id": "src.common.rpc_server", "ignore_all": false, "interface_hash": "309dbbcea2e8a87c20c779888b11d78661d126f6680c08eb0114b91f92444ca2", "mtime": 1753508901, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src/common/rpc_server.py", "plugin_data": null, "size": 12684, "suppressed": [], "version_id": "1.8.0"}