["test_guard_fort_exceptions.py::test_custom_exceptions", "test_guard_fort_exceptions.py::test_debug_vs_production_mode", "test_guard_fort_exceptions.py::test_exception_logging_integration", "test_guard_fort_exceptions.py::test_service_health_monitoring", "test_guard_fort_exceptions.py::test_service_integration", "tests/test_agent_manifest.py::TestAgentDiscovery::test_call_agent_method", "tests/test_agent_manifest.py::TestAgentDiscovery::test_discover_agent_failure", "tests/test_agent_manifest.py::TestAgentDiscovery::test_discover_agent_success", "tests/test_agent_manifest.py::TestAgentDiscovery::test_get_cached_manifest", "tests/test_agent_manifest.py::TestAgentDiscovery::test_list_agent_methods", "tests/test_agent_manifest.py::TestAgentManifestGenerator::test_add_capability", "tests/test_agent_manifest.py::TestAgentManifestGenerator::test_docstring_parsing", "tests/test_agent_manifest.py::TestAgentManifestGenerator::test_generator_initialization", "tests/test_agent_manifest.py::TestAgentManifestGenerator::test_manifest_generation", "tests/test_agent_manifest.py::TestAgentManifestGenerator::test_parameter_schema_extraction", "tests/test_agent_manifest.py::TestAgentManifestGenerator::test_register_method", "tests/test_agent_manifest.py::TestAgentManifestGenerator::test_to_dict_and_json", "tests/test_agent_manifest.py::TestJsonRpcServerIntegration::test_custom_capabilities", "tests/test_agent_manifest.py::TestJsonRpcServerIntegration::test_endpoints_and_metadata", "tests/test_agent_manifest.py::TestJsonRpcServerIntegration::test_enhanced_manifest_generation", "tests/test_agent_manifest.py::TestMethodSchema::test_method_schema_creation", "tests/test_agent_manifest.py::TestParameterSchema::test_parameter_schema_creation", "tests/test_guard_fort.py::TestGuardFortCore::test_demo_authentication", "tests/test_guard_fort.py::TestGuardFortCore::test_enhanced_authentication", "tests/test_guard_fort.py::TestGuardFortCore::test_exception_handling", "tests/test_guard_fort.py::TestGuardFortCore::test_exception_logging", "tests/test_guard_fort.py::TestGuardFortCore::test_health_endpoint_auth_bypass", "tests/test_guard_fort.py::TestGuardFortCore::test_request_id_generation", "tests/test_guard_fort.py::TestGuardFortCore::test_request_id_propagation", "tests/test_guard_fort.py::TestGuardFortCore::test_request_logging", "tests/test_guard_fort.py::TestGuardFortCore::test_security_headers", "tests/test_guard_fort.py::TestGuardFortCore::test_security_headers_disabled", "tests/test_guard_fort.py::TestGuardFortCore::test_service_headers", "tests/test_guard_fort.py::TestGuardFortCore::test_timing_functionality", "tests/test_guard_fort.py::TestGuardFortIntegration::test_multiple_requests_with_different_ids", "tests/test_guard_fort.py::TestGuardFortSecurity::test_api_key_validation", "tests/test_guard_fort.py::TestGuardFortSecurity::test_authentication_schemes", "tests/test_guard_fort.py::TestGuardFortSecurity::test_custom_allowed_paths", "tests/test_guard_fort.py::TestGuardFortSecurity::test_token_validation_methods", "tests/test_guard_fort.py::TestGuardFortUtilities::test_guard_fort_disabled_auth", "tests/test_guard_fort.py::TestGuardFortUtilities::test_init_guard_fort_utility", "tests/test_json_rpc_models.py::TestJsonRpcError::test_custom_error_codes", "tests/test_json_rpc_models.py::TestJsonRpcError::test_error_without_data", "tests/test_json_rpc_models.py::TestJsonRpcError::test_invalid_error_codes", "tests/test_json_rpc_models.py::TestJsonRpcError::test_valid_error_creation", "tests/test_json_rpc_models.py::TestJsonRpcRequest::test_invalid_jsonrpc_version", "tests/test_json_rpc_models.py::TestJsonRpcRequest::test_invalid_method_names", "tests/test_json_rpc_models.py::TestJsonRpcRequest::test_request_serialization", "tests/test_json_rpc_models.py::TestJsonRpcRequest::test_request_without_params", "tests/test_json_rpc_models.py::TestJsonRpcRequest::test_valid_request_creation", "tests/test_json_rpc_models.py::TestJsonRpcResponse::test_error_response", "tests/test_json_rpc_models.py::TestJsonRpcResponse::test_invalid_response_validation", "tests/test_json_rpc_models.py::TestJsonRpcResponse::test_missing_result_and_error", "tests/test_json_rpc_models.py::TestJsonRpcResponse::test_response_serialization", "tests/test_json_rpc_models.py::TestJsonRpcResponse::test_successful_response", "tests/test_json_rpc_models.py::TestModelSerialization::test_model_config_extra_forbid", "tests/test_json_rpc_models.py::TestModelSerialization::test_round_trip_serialization", "tests/test_json_rpc_models.py::TestSharedDataModels::test_advice_request_model", "tests/test_json_rpc_models.py::TestSharedDataModels::test_answer_response_model", "tests/test_json_rpc_models.py::TestSharedDataModels::test_query_request_model", "tests/test_json_rpc_models.py::TestSharedDataModels::test_search_request_model", "tests/test_json_rpc_models.py::TestSharedDataModels::test_snippet_model", "tests/test_project_setup.py::test_ci_cd_pipeline", "tests/test_project_setup.py::test_code_quality_tools", "tests/test_project_setup.py::test_component_requirements_exist", "tests/test_project_setup.py::test_config_loading", "tests/test_project_setup.py::test_configuration_management", "tests/test_project_setup.py::test_imports", "tests/test_project_setup.py::test_poetry_configuration", "tests/test_project_setup.py::test_project_structure", "tests/test_project_setup.py::test_python_files_exist", "tests/test_rpc_client.py::test_json_rpc_client_auto_id_generation", "tests/test_rpc_client.py::test_json_rpc_client_call_http_error", "tests/test_rpc_client.py::test_json_rpc_client_call_request_error", "tests/test_rpc_client.py::test_json_rpc_client_call_success", "tests/test_rpc_client.py::test_json_rpc_client_custom_timeout", "tests/test_rpc_server.py::TestGlobalServerDecorator::test_get_global_server", "tests/test_rpc_server.py::TestGlobalServerDecorator::test_rpc_method_decorator", "tests/test_rpc_server.py::TestJsonRpcMethodRegistry::test_duplicate_method_registration", "tests/test_rpc_server.py::TestJsonRpcMethodRegistry::test_method_registration", "tests/test_rpc_server.py::TestJsonRpcMethodRegistry::test_method_schema_extraction", "tests/test_rpc_server.py::TestJsonRpcMethodRegistry::test_reserved_method_names", "tests/test_rpc_server.py::TestJsonRpcServer::test_batch_requests", "tests/test_rpc_server.py::TestJsonRpcServer::test_context_injection", "tests/test_rpc_server.py::TestJsonRpcServer::test_empty_batch_request", "tests/test_rpc_server.py::TestJsonRpcServer::test_handler_exception", "tests/test_rpc_server.py::TestJsonRpcServer::test_invalid_params", "tests/test_rpc_server.py::TestJsonRpcServer::test_manifest_generation", "tests/test_rpc_server.py::TestJsonRpcServer::test_method_decorator", "tests/test_rpc_server.py::TestJsonRpcServer::test_method_not_found", "tests/test_rpc_server.py::TestJsonRpcServer::test_middleware", "tests/test_rpc_server.py::TestJsonRpcServer::test_notification_request", "tests/test_rpc_server.py::TestJsonRpcServer::test_parse_error", "tests/test_rpc_server.py::TestJsonRpcServer::test_server_initialization", "tests/test_rpc_server.py::TestJsonRpcServer::test_valid_request_handling", "tests/test_rpc_server.py::TestUtilityFunctions::test_create_error_response", "tests/test_rpc_server.py::TestUtilityFunctions::test_create_success_response", "tests/test_rpc_server.py::TestUtilityFunctions::test_logging_middleware"]